#!/usr/bin/env python3
"""
Script de test d'intégration pour le module Confluence dans kbot-load-scheduler.

Ce script teste l'intégration complète avec une vraie instance Confluence.
Il peut être utilisé pour valider que l'intégration fonctionne correctement.

Usage:
    python test_confluence_integration.py

Variables d'environnement requises:
    CONFLUENCE_URL - URL de l'instance Confluence
    DEFAULT_SPACE_KEY - Espace par défaut (optionnel)
    
Secrets Manager requis:
    test-confluence-credentials - JSON avec pat_token ou username/api_token
"""

import os
import sys
import json
import logging
from datetime import datetime, timezone

# Ajouter le répertoire src au path
sys.path.insert(0, 'src')

from kbotloadscheduler.loader.confluence_loader import ConfluenceLoader, LoaderException
from kbotloadscheduler.bean.beans import SourceBean, DocumentBean
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret


def setup_logging():
    """Configure le logging pour les tests"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def create_test_source() -> SourceBean:
    """Crée une source de test pour Confluence"""
    config = {
        "spaces": [os.getenv("DEFAULT_SPACE_KEY", "TEST")],
        "max_results": 10,  # Limité pour les tests
        "include_attachments": True,
        "content_types": ["page", "blogpost"],
        "last_modified_days": 30  # Seulement les documents récents
    }
    
    return SourceBean(
        id=1,
        code="test_confluence_integration",
        label="Test Confluence Integration",
        src_type="confluence",
        configuration=json.dumps(config),
        last_load_time=int(datetime.now(timezone.utc).timestamp()),
        load_interval=24,
        domain_code="test_domain",
        perimeter_code="test",  # Utilisé pour les credentials
        force_embedding=False
    )


def test_confluence_loader():
    """Test principal du ConfluenceLoader"""
    print("🧪 Test d'intégration du ConfluenceLoader")
    print("=" * 50)
    
    # Vérifier les variables d'environnement
    confluence_url = os.getenv("CONFLUENCE_URL")
    if not confluence_url:
        print("❌ ERREUR: Variable d'environnement CONFLUENCE_URL manquante")
        print("   Exemple: export CONFLUENCE_URL=https://mycompany.atlassian.net")
        return False
    
    print(f"✅ URL Confluence: {confluence_url}")
    print(f"✅ Espace par défaut: {os.getenv('DEFAULT_SPACE_KEY', 'TEST')}")
    
    try:
        # Créer le mock de ConfigWithSecret
        class MockConfigWithSecret:
            def get_confluence_credentials(self, perimeter_code):
                # Essayer de récupérer depuis les variables d'environnement pour les tests
                pat_token = os.getenv("CONFLUENCE_PAT_TOKEN")
                username = os.getenv("CONFLUENCE_USERNAME")
                api_token = os.getenv("CONFLUENCE_API_TOKEN")
                
                if pat_token:
                    return {"pat_token": pat_token}
                elif username and api_token:
                    return {"username": username, "api_token": api_token}
                else:
                    # Credentials par défaut pour les tests (ne fonctionneront pas)
                    return {"pat_token": "test_token_will_fail"}
        
        config_with_secret = MockConfigWithSecret()
        
        # Créer le loader
        print("\n📦 Création du ConfluenceLoader...")
        loader = ConfluenceLoader(config_with_secret)
        print("✅ ConfluenceLoader créé avec succès")
        
        # Créer la source de test
        print("\n📋 Création de la source de test...")
        source = create_test_source()
        print(f"✅ Source créée: {source.code}")
        print(f"   Configuration: {source.configuration}")
        
        # Test 1: Récupérer la liste des documents
        print("\n📄 Test 1: Récupération de la liste des documents...")
        try:
            documents = loader.get_document_list(source)
            print(f"✅ Liste récupérée avec succès: {len(documents)} documents trouvés")
            
            # Afficher quelques exemples
            for i, doc in enumerate(documents[:3]):
                print(f"   📄 Document {i+1}: {doc.name} (ID: {doc.id})")
            
            if len(documents) > 3:
                print(f"   ... et {len(documents) - 3} autres documents")
                
        except LoaderException as e:
            print(f"⚠️  Erreur lors de la récupération de la liste: {e}")
            print("   Cela peut être normal si les credentials ne sont pas configurés")
            return False
        
        # Test 2: Récupérer un document spécifique (si on en a trouvé)
        if documents:
            print("\n💾 Test 2: Récupération d'un document spécifique...")
            test_document = documents[0]
            output_path = "gs://test-bucket/test-path/confluence-test"
            
            try:
                metadata = loader.get_document(source, test_document, output_path)
                print("✅ Document récupéré avec succès")
                print(f"   Métadonnées: {list(metadata.keys())}")
                
            except LoaderException as e:
                print(f"⚠️  Erreur lors de la récupération du document: {e}")
                print("   Cela peut être normal sans accès GCS configuré")
        
        print("\n🎉 Tests d'intégration terminés avec succès !")
        return True
        
    except Exception as e:
        print(f"❌ ERREUR inattendue: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_via_container():
    """Test via le container de dépendances (si disponible)"""
    print("\n🏗️  Test via le container de dépendances")
    print("=" * 50)
    
    try:
        from kbotloadscheduler.dependency.container import Container
        
        # Créer le container
        container = Container()
        loader_manager = container.loader_manager()
        
        # Récupérer le loader Confluence
        confluence_loader = loader_manager.get_loader("confluence")
        print("✅ ConfluenceLoader récupéré via le container")
        
        # Vérifier que c'est bien notre loader
        assert isinstance(confluence_loader, ConfluenceLoader)
        print("✅ Type de loader vérifié")
        
        return True
        
    except ImportError:
        print("⚠️  Container non disponible (dépendances manquantes)")
        return False
    except Exception as e:
        print(f"❌ Erreur avec le container: {e}")
        return False


def main():
    """Fonction principale"""
    setup_logging()
    
    print("🚀 Test d'intégration du module Confluence")
    print("=" * 60)
    
    # Informations sur l'environnement
    print(f"Python: {sys.version}")
    print(f"Répertoire de travail: {os.getcwd()}")
    
    success = True
    
    # Test 1: ConfluenceLoader direct
    if not test_confluence_loader():
        success = False
    
    # Test 2: Via le container
    if not test_via_container():
        success = False
    
    # Résumé
    print("\n" + "=" * 60)
    if success:
        print("🎉 TOUS LES TESTS SONT PASSÉS !")
        print("\nLe module Confluence est correctement intégré dans kbot-load-scheduler.")
        print("\nPour tester avec une vraie instance Confluence:")
        print("1. Configurez CONFLUENCE_URL")
        print("2. Configurez les credentials:")
        print("   - CONFLUENCE_PAT_TOKEN (recommandé)")
        print("   - ou CONFLUENCE_USERNAME + CONFLUENCE_API_TOKEN")
        print("3. Relancez ce script")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("\nVérifiez la configuration et les logs ci-dessus.")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
