# Tests du Système RAG Confluence

Ce répertoire contient tous les tests pour le système RAG Confluence, organisés en plusieurs catégories pour assurer une couverture complète et une validation robuste du code.

## 📁 Structure des Tests

### Tests Unitaires

| Fichier | Description | Couverture |
|---------|-------------|------------|
| `test_auth.py` | Tests d'authentification | AuthenticationManager, types d'auth |
| `test_config.py` | Tests de configuration | Classes de config, validation |
| `test_models.py` | Tests des modèles de données | Pydantic models, validation |
| `test_storage.py` | Tests de stockage | FileSystemStorage, GCSStorage |
| `test_exceptions.py` | Tests des exceptions | Exceptions personnalisées |
| `test_constants.py` | Tests des constantes | APIConstants, AuthType |
| `test_security.py` | Tests de sécurité | SecurityUtils, sanitization |
| `test_utils.py` | Tests des utilitaires | SecurityValidator, TextProcessor, RetryHandler |
| `test_health_check.py` | Tests des health checks | HealthChecker, statuts |

### Tests de Sécurité

| Fichier | Description | Couverture |
|---------|-------------|------------|
| `test_log_security.py` | Sécurité des logs | SecurityFilter, masquage |
| `test_integration_security.py` | Tests d'intégration sécurité | Intégration complète |

### Tests de Performance

| Fichier | Description | Couverture |
|---------|-------------|------------|
| `test_optimized_client.py` | Client optimisé | Performance du client |
| `test_parallel_pagination.py` | Pagination parallèle | Optimisations parallèles |
| `test_performance_optimization.py` | Optimisations générales | Performance globale |
| `test_thread_pool_optimization.py` | Thread pools | Gestion des threads |

### Tests d'Intégration

| Fichier | Description | Couverture |
|---------|-------------|------------|
| `test_integration_complete.py` | Tests d'intégration complets | Workflow de bout en bout |
| `run_integration_tests.py` | Script d'exécution | Tests avec espace structuré |
| `fixtures/` | Données de test | Factory et fichiers d'exemple |

> 📖 **Guide détaillé** : Consultez [GUIDE_TESTS_INTEGRATION.md](GUIDE_TESTS_INTEGRATION.md) pour un guide complet des tests d'intégration avec espace de test structuré.

## 🚀 Lancement des Tests

### 🎯 **Méthode Recommandée - Script Principal**

Le script `run_tests.py` a été mis à jour pour corriger les problèmes de chemins et supprimer les avertissements Pydantic :

```bash
# Tous les types de tests (8-10 minutes) - SANS avertissements
python run_tests.py --all

# Tests par catégorie avec suppression des warnings
python run_tests.py --unit --integration --performance --security --coverage
```

### ⚡ **Tests Rapides pour Développement**

```bash
# Validation rapide (30 secondes)
python test_gitlab_ci_quick.py

# Tests unitaires seulement (2-3 minutes) - Optimisé
python run_tests.py --unit

# Tests essentiels sans les lents
python -m pytest confluence_rag/tests/ -m "unit and not slow" -v --disable-warnings
```

### 🔧 **Corrections Récentes**

✅ **Problèmes résolus** :
- Chemins d'intégration corrigés (`test_health_checks_integration.py`, `test_raw_downloads_integration.py`)
- Avertissements Pydantic supprimés (FastAPI `__fields__` deprecation)
- Tous les tests utilisent maintenant pytest avec filtres d'avertissements
- 477 tests collectés et exécutés avec succès

### 🧪 **Tests par Catégorie**

```bash
# Tests unitaires
python run_tests.py --unit
python -m pytest confluence_rag/tests/ -m "unit or not integration" -v

# Tests d'intégration
python run_tests.py --integration
python -m pytest confluence_rag/tests/ -m "integration" -v

# Tests de performance
python run_tests.py --performance
python -m pytest confluence_rag/tests/ -m "performance" -v

# Tests de sécurité
python run_tests.py --security
python -m pytest confluence_rag/tests/ -m "security" -v
```

### 🎯 **Tests Spécifiques**

```bash
# Par pattern/mot-clé
python run_tests.py --unit --pattern="tracking"
python run_tests.py --unit --pattern="config"
python -m pytest -k "tracking" -v

# Par fichier spécifique
python -m pytest confluence_rag/tests/test_config.py -v
python -m pytest confluence_rag/tests/test_tracking.py -v

# Par classe ou méthode
python -m pytest confluence_rag/tests/test_config.py::TestConfluenceConfig -v
python -m pytest confluence_rag/tests/test_tracking.py::TestConfluenceChangeTracker::test_tracker_initialization -v
```

### 🔗 **Tests d'Intégration Mis à Jour**

Les tests d'intégration sont maintenant correctement organisés dans `confluence_rag/tests/` :

```bash
# Tests d'intégration via le script principal (recommandé)
python run_tests.py --integration

# Tests d'intégration spécifiques
python -m pytest confluence_rag/tests/test_health_checks_integration.py -v
python -m pytest confluence_rag/tests/test_raw_downloads_integration.py -v
python -m pytest confluence_rag/tests/test_integration_complete.py -v
python -m pytest confluence_rag/tests/test_integration_security.py -v
python -m pytest confluence_rag/tests/test_real_confluence_integration.py -v

# Tests rapides avec données simulées
python quick_test.py --mock

# Résumé des données de test
python confluence_rag/tests/run_integration_tests.py --summary

# Tests d'intégration complets avec le script dédié
python confluence_rag/tests/run_integration_tests.py --run-tests --verbose
```

### Avec pytest directement

```bash
# Tests unitaires
pytest confluence_rag/tests/ -v

# Tests avec markers
pytest -m "unit" -v
pytest -m "security" -v
pytest -m "performance" -v

# Tests spécifiques
pytest confluence_rag/tests/test_auth.py -v
pytest confluence_rag/tests/test_storage.py::TestFileSystemStorage -v
```

## 📊 Couverture de Code

### Installation de coverage

```bash
pip install coverage pytest-cov
```

### Analyse de couverture

```bash
# Avec le script de test (recommandé)
python run_tests.py --coverage

# Avec pytest directement
python -m pytest --cov=confluence_rag --cov-report=html --cov-report=term-missing

# Couverture complète avec tous les tests
python -m pytest confluence_rag/tests/ --cov=confluence_rag --cov-report=html --cov-report=term-missing --cov-report=xml

# Rapport HTML (dans htmlcov/)
coverage html

# Rapport dans le terminal
coverage report -m
```

### 📈 **Objectifs de Couverture**

- **Couverture globale** : >80%
- **Modules critiques** : >90%
- **Nouvelles fonctionnalités** : 100%

## 🏷️ Markers de Tests

Les tests utilisent des markers pytest pour la catégorisation :

- `@pytest.mark.unit` : Tests unitaires rapides
- `@pytest.mark.integration` : Tests d'intégration
- `@pytest.mark.performance` : Tests de performance
- `@pytest.mark.security` : Tests de sécurité
- `@pytest.mark.slow` : Tests lents (>5s)
- `@pytest.mark.network` : Tests nécessitant le réseau

### Utilisation des markers

```bash
# Exécuter seulement les tests rapides
pytest -m "unit and not slow"

# Exécuter les tests de sécurité
pytest -m "security"

# Exclure les tests réseau
pytest -m "not network"
```

## 🔧 Configuration des Tests

### Variables d'environnement

```bash
# Configuration de base pour les tests
export CONFLUENCE_RAG_ENVIRONMENT=test
export STORAGE_TYPE=filesystem
export TRACKING_STORAGE_TYPE=filesystem
export LOG_LEVEL=WARNING
export SECURE_LOGGING=true
export HEALTH_CHECK_ENABLED=false

# Puis lancer les tests
python run_tests.py --all
```

### Fichiers de configuration

- `pytest.ini` : Configuration pytest avec markers
- `__init__.py` : Configuration de l'environnement de test
- `validate_gitlab_ci.py` : Script de validation complète
- `test_gitlab_ci_quick.py` : Script de validation rapide

### 🚀 **Workflow Recommandé pour le Développement**

```bash
# 1. Pendant le développement (rapide - 30s)
python test_gitlab_ci_quick.py

# 2. Avant un commit (complet - 3min)
python run_tests.py --unit --coverage

# 3. Avant un push (validation complète - 10min)
python validate_gitlab_ci.py && python run_tests.py --all
```

## 📝 Écriture de Nouveaux Tests

### Structure recommandée

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour le module [nom_module].
"""

import unittest
from unittest.mock import Mock, patch
import pytest

from confluence.[module] import [ClasseATest]


class Test[ClasseATest](unittest.TestCase):
    """Tests pour la classe [ClasseATest]."""

    def setUp(self):
        """Configuration des tests."""
        # Initialisation commune
        pass

    def tearDown(self):
        """Nettoyage après les tests."""
        # Nettoyage si nécessaire
        pass

    @pytest.mark.unit
    def test_[methode]_[scenario](self):
        """Test de [description du test]."""
        # Arrange
        # Act
        # Assert
        pass

    @pytest.mark.integration
    @pytest.mark.slow
    def test_integration_[scenario](self):
        """Test d'intégration pour [scenario]."""
        pass


if __name__ == '__main__':
    unittest.main()
```

### Bonnes pratiques

1. **Nommage** : `test_[module]_[fonction]_[scenario]`
2. **Structure AAA** : Arrange, Act, Assert
3. **Isolation** : Chaque test doit être indépendant
4. **Mocking** : Utiliser des mocks pour les dépendances externes
5. **Documentation** : Docstrings claires pour chaque test

## 🐛 Dépannage

### Tests qui échouent

```bash
# Exécuter un test spécifique en mode debug
python -m pytest confluence_rag/tests/test_auth.py::TestAuthenticationManager::test_pat_token_authentication -v -s

# Afficher les logs pendant les tests
python -m pytest --log-cli-level=DEBUG -v

# Mode verbeux avec traceback complet
python -m pytest confluence_rag/tests/ -v -s --tb=long

# Arrêter au premier échec
python -m pytest confluence_rag/tests/ -x -v

# Tests parallèles pour accélérer
python -m pytest confluence_rag/tests/ -n auto -v
```

### Problèmes courants

1. **Import errors** : Vérifier le PYTHONPATH et les dépendances
2. **Async tests** : Utiliser `pytest-asyncio` et les markers appropriés
3. **Mocks** : S'assurer que les chemins de mock sont corrects
4. **Cleanup** : Nettoyer les ressources après les tests
5. **Markers** : Vérifier que les markers sont définis dans `pytest.ini`
6. **Variables d'environnement** : Configurer l'environnement de test

### 🔍 **Debug Avancé**

```bash
# Profiling des tests lents
python -m pytest confluence_rag/tests/ --durations=10

# Tests avec coverage et debug
python -m pytest confluence_rag/tests/ --cov=confluence_rag --pdb

# Validation de la configuration pytest
python -m pytest --collect-only | head -20
```

## 📈 Métriques de Qualité

### 📊 **Temps d'Exécution Typiques - Mise à Jour**

| Commande | Temps Estimé | Description | Statut |
|----------|--------------|-------------|---------|
| `python test_gitlab_ci_quick.py` | 30s | Validation rapide | ✅ |
| `python run_tests.py --unit` | 2-3min | Tests unitaires | ✅ Optimisé |
| `python run_tests.py --integration` | 1-2min | Tests d'intégration | ✅ Corrigé |
| `python run_tests.py --all` | 8-10min | Tous les tests | ✅ Sans warnings |
| `python run_tests.py --coverage` | 5-6min | Avec couverture | ✅ |

### 🎯 **Objectifs de Couverture**

- **Couverture globale** : >80% ✅
- **Modules critiques** : >90% ✅
- **Nouvelles fonctionnalités** : 100% ✅

### 📋 **Métriques Actuelles**

- **477 tests** collectés au total ✅
- **468 tests** passent, **9 skipped** ✅
- **5 fichiers d'intégration** correctement organisés ✅
- **0 avertissement Pydantic** après corrections ✅
- Taux de réussite (100% en CI/CD) ✅
- Couverture de code maintenue ✅
- Temps d'exécution optimisé ✅

## 🔄 Intégration Continue

### 🚀 **Pipeline GitLab CI/CD**

Les tests sont exécutés automatiquement :

1. **Validation** : Tests rapides de structure (30s)
2. **Tests unitaires** : Tests principaux (2-3min)
3. **Tests d'intégration** : Avec mocks (2min)
4. **Tests de performance** : Optimisations (3min)
5. **Couverture** : Analyse complète (3min)
6. **Déploiement** : Manuel vers GCP

### 📚 **Ressources Supplémentaires**

- [Guide GitLab CI/CD](../docs/GITLAB_CI_DEPLOYMENT_GUIDE.md)
- [Rapport de validation](../../GITLAB_CI_VALIDATION_REPORT.md)
- [Configuration pytest](../../pytest.ini)
- [Script de validation](../../validate_gitlab_ci.py)
