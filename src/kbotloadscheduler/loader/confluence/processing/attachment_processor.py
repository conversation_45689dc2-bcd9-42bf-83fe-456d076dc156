#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Attachment processing for Confluence RAG system.
"""

import logging
import asyncio
import os
from typing import Optional, Dict

from .enums import ProcessingStatus
from .document_extractors import DocumentExtractor
from ..models import AttachmentDetail
from ..client import ConfluenceClient
from ..config import ProcessingConfig
from ..utils import SecurityValidator
from ..thread_pool_manager import get_thread_pool_manager
from ..exceptions import AttachmentProcessingError


class AttachmentProcessor:
    """Enhanced processor for Confluence attachments with improved error handling and logging."""

    def __init__(self, client: ConfluenceClient, processing_config: Optional[ProcessingConfig] = None, storage_config=None):
        """
        Initialize the processor with Confluence client and processing configuration.

        Args:
            client: Confluence client for API calls
            processing_config: Processing configuration (optional)
            storage_config: Storage configuration to determine which files to process
        """
        self.client = client
        self.logger = logging.getLogger(__name__)
        self.storage_config = storage_config

        # Use provided configuration or create default
        self.config = processing_config or ProcessingConfig.from_env()

        # Initialize document extractor
        self.extractor = DocumentExtractor(self.logger)

        # Use centralized thread pool manager instead of local ThreadPoolExecutor
        self.thread_pool_manager = get_thread_pool_manager(self.config.thread_pool_config)

        # Create semaphore to limit concurrent downloads
        self.download_semaphore = asyncio.Semaphore(self.config.max_parallel_downloads)

        # Track processing statistics
        self._stats = {
            "processed": 0,
            "failed": 0,
            "skipped": 0
        }

        self.logger.info(
            f"AttachmentProcessor initialized with centralized thread pool manager, "
            f"max_parallel_downloads={self.config.max_parallel_downloads}"
        )

    async def process_attachment(self, attachment: AttachmentDetail) -> AttachmentDetail:
        """
        Process an attachment to extract its text content or mark for raw download.

        Args:
            attachment: Attachment details to process

        Returns:
            AttachmentDetail with extracted text and processing status
        """
        start_time = asyncio.get_event_loop().time()

        try:
            # Validate attachment security
            SecurityValidator.validate_attachment(attachment.file_name, attachment.media_type)

            # Update processing status
            attachment.processing_status = ProcessingStatus.PROCESSING.value

            # Check if this file should be downloaded raw instead of processed
            if self.storage_config:
                _, ext = os.path.splitext(attachment.file_name.lower())
                should_download_raw = ext in self.storage_config.attachment_extensions_to_download_raw
                should_convert = ext in self.storage_config.attachment_extensions_to_convert

                if should_download_raw and not should_convert:
                    # Mark for raw download, skip text extraction
                    self.logger.info(f"Marking attachment for raw download: {attachment.file_name}")
                    attachment.extracted_text = f"[Raw file: {attachment.file_name}]"
                    attachment.processing_status = ProcessingStatus.COMPLETED.value
                    attachment.processing_metadata = {"type": "raw_download", "extension": ext}
                    self._stats["skipped"] += 1
                    return attachment

            # Check if document type is supported for text extraction
            if not self.extractor.can_process(attachment.file_name, attachment.media_type):
                self.logger.warning(f"Unsupported attachment type: {attachment.file_name} ({attachment.media_type})")
                attachment.extracted_text = f"[Unsupported document: {attachment.file_name}]"
                attachment.processing_status = ProcessingStatus.COMPLETED.value
                self._stats["skipped"] += 1
                return attachment

            # Download attachment with semaphore limiting concurrent downloads
            async with self.download_semaphore:
                self.logger.debug(f"Downloading attachment {attachment.id} - {attachment.file_name}")
                content_bytes = await self.client.download_attachment(attachment)
                download_time = asyncio.get_event_loop().time() - start_time
                self.logger.debug(
                    f"Download completed for {attachment.id} - {attachment.file_name} "
                    f"({len(content_bytes)} bytes in {download_time:.2f}s)"
                )

            # Extract text using thread pool for CPU-intensive operations
            extraction_result = await self.thread_pool_manager.run_in_document_pool(
                self.extractor.extract_text,
                content_bytes,
                attachment.file_name,
                attachment.media_type
            )

            # Update attachment with extraction results
            if extraction_result.success:
                attachment.extracted_text = extraction_result.text
                attachment.processing_status = ProcessingStatus.COMPLETED.value
                attachment.processing_metadata = extraction_result.metadata
                self._stats["processed"] += 1

                processing_time = asyncio.get_event_loop().time() - start_time
                self.logger.info(
                    f"Successfully processed attachment {attachment.id} - {attachment.file_name} "
                    f"in {processing_time:.2f}s"
                )
            else:
                attachment.extracted_text = extraction_result.text or f"[Processing failed: {attachment.file_name}]"
                attachment.processing_status = ProcessingStatus.FAILED.value
                attachment.processing_error = extraction_result.error_message
                self._stats["failed"] += 1

                self.logger.error(
                    f"Text extraction failed for attachment {attachment.id} - {attachment.file_name}: "
                    f"{extraction_result.error_message}"
                )

            return attachment

        except Exception as e:
            processing_time = asyncio.get_event_loop().time() - start_time
            self.logger.error(
                f"Attachment processing failed for {attachment.id} - {attachment.file_name} "
                f"after {processing_time:.2f}s: {e}"
            )

            attachment.processing_status = ProcessingStatus.FAILED.value
            attachment.processing_error = str(e)
            attachment.extracted_text = f"[Processing error: {attachment.file_name}]"
            self._stats["failed"] += 1

            if isinstance(e, AttachmentProcessingError):
                raise
            else:
                raise AttachmentProcessingError(
                    f"Attachment processing failed: {e}",
                    attachment_id=attachment.id,
                    file_name=attachment.file_name
                )

    def get_processing_stats(self) -> Dict[str, int]:
        """Get processing statistics."""
        return self._stats.copy()

    def reset_stats(self) -> None:
        """Reset processing statistics."""
        self._stats = {
            "processed": 0,
            "failed": 0,
            "skipped": 0
        }
