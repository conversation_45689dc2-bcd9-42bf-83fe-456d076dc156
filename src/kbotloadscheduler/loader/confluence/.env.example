# Configuration globale de l'environnement
CONFLUENCE_RAG_ENVIRONMENT=dev  # Options: dev, staging, prod
CONFLUENCE_RAG_CONFIG_FILE=/path/to/config.json  # Optionnel, pour charger depuis un fichier JSON

# Configuration Confluence par défaut
CONFLUENCE_URL=https://example.atlassian.net
CONFLUENCE_USERNAME=<EMAIL>
CONFLUENCE_API_TOKEN=your_api_token
DEFAULT_SPACE_KEY=EXAMPLE

# Configuration pour l'environnement DEV
DEV_CONFLUENCE_URL=https://dev-example.atlassian.net
DEV_CONFLUENCE_PAT_TOKEN=your_dev_pat_token
DEV_DEFAULT_SPACE_KEY=DEV
DEV_IO_THREAD_WORKERS=4
DEV_DOCUMENT_PROCESSING_WORKERS=2

# Configuration pour l'environnement STAGING
STAGING_CONFLUENCE_URL=https://staging-example.atlassian.net
STAGING_CONFLUENCE_PAT_TOKEN=your_staging_pat_token
STAGING_DEFAULT_SPACE_KEY=STAGING
STAGING_MAX_PARALLEL_REQUESTS=5
STAGING_RETRY_MAX_ATTEMPTS=5

# Configuration pour l'environnement PROD
PROD_CONFLUENCE_URL=https://prod-example.atlassian.net
PROD_CONFLUENCE_PAT_TOKEN=your_prod_pat_token
PROD_DEFAULT_SPACE_KEY=PROD
PROD_MAX_PARALLEL_REQUESTS=10
PROD_IO_THREAD_WORKERS=16
PROD_DOCUMENT_PROCESSING_WORKERS=8
PROD_API_THREAD_WORKERS=5
