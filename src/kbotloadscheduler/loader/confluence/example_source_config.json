{"description": "Exemple de configuration pour une source Confluence dans kbot-load-scheduler", "source_bean_example": {"id": 1, "code": "confluence_docs", "label": "Documentation Confluence", "src_type": "confluence", "configuration": {"spaces": ["DOCS", "TECH", "PROJ"], "max_results": 1000, "include_attachments": true, "content_types": ["page", "blogpost"], "last_modified_days": 30, "labels": ["public", "documentation"], "exclude_labels": ["draft", "private"], "title_contains": "API"}, "last_load_time": 1234567890, "load_interval": 24, "domain_code": "engineering", "perimeter_code": "main", "force_embedding": false}, "configuration_options": {"spaces": {"description": "Liste des espaces Confluence à synchroniser", "type": "array", "required": true, "example": ["DOCS", "TECH"]}, "max_results": {"description": "Nombre maximum de documents à récupérer", "type": "integer", "default": 1000, "example": 500}, "include_attachments": {"description": "Inclure les pièces jointes dans la synchronisation", "type": "boolean", "default": true, "example": false}, "content_types": {"description": "Types de contenu à synchroniser", "type": "array", "default": ["page", "blogpost"], "options": ["page", "blogpost", "comment"], "example": ["page"]}, "last_modified_days": {"description": "Synchroniser seulement les documents modifiés dans les N derniers jours", "type": "integer", "optional": true, "example": 7}, "labels": {"description": "Synchroniser seulement les documents avec ces labels", "type": "array", "optional": true, "example": ["public", "api"]}, "exclude_labels": {"description": "Exclure les documents avec ces labels", "type": "array", "optional": true, "example": ["draft", "private"]}, "title_contains": {"description": "Synchroniser seulement les documents dont le titre contient cette chaîne", "type": "string", "optional": true, "example": "API"}}, "environment_variables": {"required": {"CONFLUENCE_URL": {"description": "URL de base de l'instance Confluence", "example": "https://mycompany.atlassian.net"}}, "optional": {"DEFAULT_SPACE_KEY": {"description": "Espace par défaut si non spécifié dans la configuration", "default": "EXAMPLE", "example": "DOCS"}, "CONFLUENCE_TIMEOUT": {"description": "Timeout en secondes pour les requêtes Confluence", "default": "30", "example": "60"}}}, "secret_manager_keys": {"per_perimeter": {"description": "Clés spécifiques à un périmètre (recommandé)", "pattern": "{perimeter_code}-confluence-credentials", "example_key": "main-confluence-credentials", "format": "JSON", "content": {"pat_token": "your_personal_access_token_here"}}, "global": {"description": "Clés globales (fallback)", "key": "confluence-credentials", "format": "JSON", "content": {"pat_token": "your_personal_access_token_here"}}, "legacy": {"description": "Format legacy avec clés séparées", "keys": ["{perimeter_code}-confluence-pat-token", "{perimeter_code}-confluence-username", "{perimeter_code}-confluence-api-token"], "example_keys": ["main-confluence-pat-token", "main-confluence-username", "main-confluence-api-token"]}}, "authentication_methods": {"personal_access_token": {"description": "Méthode recommandée - Personal Access Token (PAT)", "secret_format": {"pat_token": "your_pat_token_here"}, "advantages": ["Plus sécurisé", "Pas d'expiration automatique", "Permissions granulaires"]}, "basic_auth": {"description": "Méthode legacy - Username + API Token", "secret_format": {"username": "<EMAIL>", "api_token": "your_api_token_here"}, "note": "API tokens sont dépréciés par Atlassian"}}, "integration_notes": {"loader_type": "confluence", "abstract_loader_methods": {"get_document_list": "Récupère la liste des documents Confluence selon les critères", "get_document": "Télécharge et stocke un document spécifique sur GCS"}, "document_id_format": "{domain_code}/{source_code}/page_{confluence_page_id}", "attachment_id_format": "{domain_code}/{source_code}/attachment_{confluence_attachment_id}", "storage_integration": "Utilise les utilitaires GCS existants du projet", "metadata_fields": ["document_id", "document_name", "location", "domain_code", "source_code", "source_type", "modification_time", "confluence_sync_stats"]}}