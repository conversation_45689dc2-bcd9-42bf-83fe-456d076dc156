# Confluence RAG System

Système de Retrieval Augmented Generation (RAG) pour Confluence. Ce système permet de récupérer et traiter du contenu depuis Confluence pour l'utiliser dans des applications de génération augmentée par récupération (RAG).

> **🎉 NOUVEAU** : Ce module est maintenant **intégré dans kbot-load-scheduler** ! Vous pouvez l'utiliser via l'architecture standardisée du projet. Consultez [INTEGRATION_README.md](INTEGRATION_README.md) pour le guide d'intégration.

> **Note** : Ce fichier contient la documentation technique complète du module `confluence`. Pour une vue d'ensemble du projet global, consultez [../README.md](../README.md).

## 🚀 Modes d'utilisation

Ce module peut être utilisé de **deux façons** :

### 1. 🔗 **Intégré dans kbot-load-scheduler** (Recommandé)

- ✅ **Architecture standardisée** avec `AbstractLoader`
- ✅ **Gestion des secrets** via Secret Manager
- ✅ **API REST** unifiée (`/loader/list`, `/loader/document`)
- ✅ **Container de dépendances** pour injection
- ✅ **Tests intégrés** dans la suite de tests du projet

**Guide d'utilisation** : [INTEGRATION_README.md](INTEGRATION_README.md)

### 2. 📦 **Module standalone** (Usage avancé)

- ✅ **Contrôle total** de la configuration
- ✅ **Utilisation programmatique** directe
- ✅ **Personnalisation avancée** des workflows
- ✅ **Déploiement indépendant**

**Guide d'utilisation** : Voir sections ci-dessous

## Fonctionnalités Principales

### 🚀 Performance et Optimisation

- **Pagination parallèle** : Récupération simultanée de plusieurs pages pour les gros volumes de données (40-70% d'amélioration)
- **Session HTTP réutilisable** : Optimisation des connexions pour réduire la latence et améliorer le débit
- **Téléchargements parallèles** : Téléchargement des pièces jointes en parallèle avec un nombre configurable de téléchargements simultanés
- **Fallback intelligent** : Retour automatique vers la pagination séquentielle en cas d'erreur
- **Configuration adaptative** : Paramètres ajustables selon les besoins de performance

### 🔐 Authentification et Sécurité

- **Authentification avec token PAT** : Utilisation du Personal Access Token (PAT) pour l'authentification à Confluence
- **Protection des logs** : Masquage automatique des tokens et informations sensibles dans les logs
- **Validation des entrées** : Vérification des paramètres de configuration et des données

### 📊 Récupération et Traitement

- **Récupération de contenu** : Pages, blogs, et leurs pages enfants depuis Confluence via l'API REST
- **Navigation hiérarchique** : Récupération récursive des pages enfants jusqu'à une profondeur configurable
- **Filtrage flexible** : Basé sur les espaces, les étiquettes, les types de contenu et la date de dernière modification
- **Critères de recherche personnalisables** : Configuration via un fichier JSON local ou stocké sur Google Cloud Storage
- **Gestion des pièces jointes** : Téléchargement et traitement des pièces jointes (PDF, DOCX, PPTX, TXT, etc.)

### 💾 Stockage et Suivi

- **Options de stockage multiples** : Stockage sur système de fichiers local ou Google Cloud Storage (GCS)
- **Détection des changements** : Suivi des modifications entre les synchronisations pour un traitement efficace
- **Stratégie de mise à jour intelligente** : Traitement sélectif des contenus et pièces jointes modifiés uniquement
- **Hachage cryptographique** : Utilisation de SHA-256 pour détecter avec précision les modifications de contenu
- **Synchronisation incrémentale** : Optimisation des performances avec traitement uniquement des éléments modifiés

### 🔧 Monitoring et Debugging

- **Logging structuré** : Journalisation détaillée avec identifiants de corrélation pour le suivi et le débogage
- **Mécanismes de résilience** : Retry avec backoff exponentiel, Circuit Breaker Pattern
- **Architecture modulaire** : Conception asynchrone pour de meilleures performances

### 🏗️ Architecture de Traitement Modulaire (Nouveau !)

- **Refactorisation complète** : L'ancien fichier monolithique `processing.py` (974 lignes) a été refactorisé en 6 modules spécialisés
- **Séparation des responsabilités** : Chaque module a une fonction claire et définie
- **Maintenabilité améliorée** : Code plus lisible, testable et extensible
- **Compatibilité descendante** : Les anciens imports continuent de fonctionner avec des avertissements de dépréciation
- **Traitement spécialisé** : Support avancé pour draw.io, PDF, DOCX, XLSX et autres formats

### 🏥 Health Checks et Surveillance

- **Health checks complets** : Surveillance de tous les composants critiques du système
- **Monitoring des ressources** : Surveillance de la mémoire, disque, et utilisation des threads
- **Tests de connectivité** : Vérification automatique de l'API Confluence et du stockage
- **Endpoints Kubernetes** : Support natif des probes `/health`, `/ready`, `/live`
- **Seuils configurables** : Alertes personnalisables selon l'environnement
- **Cache intelligent** : Optimisation des performances avec mise en cache des résultats

## Architecture

### Modules principaux

- **`client.py`** - Client Confluence avec optimisations de performance
- **`config.py`** - Configuration et modèles de données
- **`processing/`** - **Architecture modulaire de traitement** (nouveau !)
  - `document_extractors.py` - Extraction de texte des documents
  - `drawio_processor.py` - Traitement spécialisé draw.io
  - `attachment_processor.py` - Traitement des pièces jointes
  - `content_chunker.py` - Découpage de texte en chunks
  - `content_retriever.py` - Orchestration de récupération
- **`storage.py`** - Stockage sur filesystem ou GCS
- **`orchestrator.py`** - Orchestration du processus de synchronisation
- **`thread_pool_manager.py`** - Gestion optimisée des pools de threads
- **`tracking.py`** - Suivi des changements
- **`logging_utils.py`** - Journalisation structurée et sécurisée
- **`health_check.py`** - Système de health checks et surveillance

### Modules utilitaires

- **`circuit_breaker.py`** - Pattern Circuit Breaker pour la résilience
- **`exceptions.py`** - Exceptions personnalisées
- **`utils.py`** - Utilitaires divers
- **`models.py`** - Modèles de données Pydantic

## Installation

### 🔗 Installation avec kbot-load-scheduler (Recommandé)

Si vous utilisez le module intégré dans kbot-load-scheduler, les dépendances sont déjà incluses dans `requirements.txt` :

```bash
# Les dépendances Confluence sont automatiquement installées
pip install -r requirements.txt
```

**Dépendances ajoutées** :

- `aiohttp>=3.8.0` - Client HTTP asynchrone
- `beautifulsoup4>=4.11.0` - Parsing HTML/XML
- `lxml>=4.9.0` - Parser XML performant
- `pypdf>=3.0.0` - Extraction PDF
- `python-docx>=0.8.11` - Traitement documents Word
- `openpyxl>=3.1.0` - Traitement fichiers Excel

### 📦 Installation standalone

```bash
# Créer un environnement virtuel
python -m venv .venv
source .venv/bin/activate  # Sur Linux/Mac
# ou
.venv\Scripts\activate     # Sur Windows

# Installer les dépendances avec pyproject.toml (moderne)
# Pour le développement (inclut les outils de test)
pip install -e ".[dev]"

# Pour la production (inclut Google Cloud Storage)
pip install -e ".[prod]"

# Installation complète (toutes les dépendances)
pip install -e ".[all]"
```

> **📋 Migration vers pyproject.toml** : Ce projet utilise maintenant le format moderne `pyproject.toml` pour la gestion des dépendances. Consultez [../MIGRATION_PYPROJECT.md](../MIGRATION_PYPROJECT.md) pour plus de détails.

## Configuration

1. Copiez le fichier `.env.example` vers `.env` et remplissez les variables d'environnement nécessaires :

```bash
cp ../.env.example ../.env
```

2. Éditez le fichier `.env` pour configurer :

   - L'URL de votre instance Confluence
   - Votre token d'authentification PAT
   - Les paramètres de recherche et de stockage

3. Personnalisez les critères de recherche dans le fichier `../criteres_recherche.json` :

```json
{
  "spaces": ["VOTRE_ESPACE"],
  "labels": ["documentation", "important"],
  "types": ["page", "blogpost"],
  "date_min": "2023-01-01",
  "date_max": "2023-12-31",
  "creators": ["user1", "user2"],
  "keywords": ["architecture", "microservices"],
  "exclude_labels": ["obsolete", "archived"],
  "max_results": 100,
  "include_attachments": true,
  "attachment_types": ["pdf", "docx", "xlsx", "pptx"],
  "include_children": true,
  "max_children_depth": 3
}
```

Les paramètres `include_children` et `max_children_depth` contrôlent la récupération récursive des pages enfants :

- `include_children` : Active ou désactive la récupération des pages enfants (défaut: true)
- `max_children_depth` : Définit la profondeur maximale de récupération (1 = enfants directs uniquement, 2 = enfants et petits-enfants, etc.)

4. Configuration de la stratégie de mise à jour dans le fichier `.env` :

```
# Configuration de traitement
CHUNK_SIZE=1000                # Taille des chunks de texte
OVERLAP_SIZE=200               # Chevauchement entre les chunks
MAX_PARALLEL_DOWNLOADS=5       # Nombre maximum de téléchargements simultanés
MAX_THREAD_WORKERS=5           # Nombre maximum de workers pour le traitement des fichiers

# Configuration de pagination parallèle
ENABLE_PARALLEL_PAGINATION=true             # Activer/désactiver la pagination parallèle
MAX_PARALLEL_REQUESTS=3                     # Nombre maximum de requêtes simultanées
PARALLEL_PAGINATION_THRESHOLD=200          # Seuil minimum de résultats pour activer la parallélisation

# Configuration de stockage
STORAGE_TYPE=filesystem        # filesystem ou gcs
OUTPUT_DIR=output_data_dir
INCLUDE_ATTACHMENTS=true
ATTACHMENT_EXTENSIONS_TO_CONVERT=.pdf,.docx,.txt
MAX_ATTACHMENT_SIZE_MB=50

# Configuration de suivi
SYNC_REPORT_PATH=last_sync_report.json
```

## Utilisation

### 🔗 Utilisation intégrée dans kbot-load-scheduler

#### Via l'API REST

```bash
# Lister les documents Confluence
curl -X POST "http://localhost:8080/loader/list/{perimeter_code}" \
  -H "Content-Type: application/json" \
  -d '{"get_list_file": "path/to/confluence_source_config.json"}'

# Récupérer un document spécifique
curl -X POST "http://localhost:8080/loader/document/{perimeter_code}" \
  -H "Content-Type: application/json" \
  -d '{"document_get_file": "path/to/document_config.json"}'
```

#### Via le code Python

```python
from kbotloadscheduler.dependency.container import Container
from kbotloadscheduler.bean.beans import SourceBean

# Initialiser le container
container = Container()
loader_manager = container.loader_manager()

# Récupérer le loader Confluence
confluence_loader = loader_manager.get_loader("confluence")

# Configuration de la source
source = SourceBean(
    src_type="confluence",
    configuration='{"spaces": ["DOCS"], "max_results": 100}',
    # ... autres champs
)

# Utilisation
documents = confluence_loader.get_document_list(source)
metadata = confluence_loader.get_document(source, document, output_path)
```

**Configuration de source pour kbot-load-scheduler** :

```json
{
  "src_type": "confluence",
  "configuration": {
    "spaces": ["DOCS", "TECH"],
    "max_results": 1000,
    "include_attachments": true,
    "content_types": ["page", "blogpost"],
    "last_modified_days": 30,
    "labels": ["public"],
    "exclude_labels": ["draft"]
  }
}
```

Voir [INTEGRATION_README.md](INTEGRATION_README.md) pour la documentation complète de l'intégration.

### 📦 Utilisation standalone

#### Synchronisation de base

```bash
python ../run_sync.py
```

### Utilisation avec Google Cloud Storage

#### Pour stocker les données sur GCS

Configurez les variables suivantes dans votre fichier `.env` :

```
STORAGE_TYPE=gcs
GCS_BUCKET_NAME=votre-bucket-gcs
GCS_BASE_PREFIX=confluence
```

#### Pour charger les critères de recherche depuis GCS

Option 1 - Spécifier le bucket et le chemin séparément :

```
CRITERIA_FILE_PATH=chemin/vers/criteres_recherche.json
CRITERIA_GCS_BUCKET=votre-bucket-gcs
```

Option 2 - Utiliser une URL GCS complète :

```
CRITERIA_FILE_PATH=gs://votre-bucket-gcs/chemin/vers/criteres_recherche.json
```

### Utilisation du module Python

```python
from confluence import ConfluenceClient, ConfluenceConfig, SearchCriteria

# Configuration
config = ConfluenceConfig(
    base_url="https://your-confluence.atlassian.net",
    username="<EMAIL>",
    token="your-pat-token"
)

criteria = SearchCriteria(
    spaces=["SPACE1", "SPACE2"],
    content_types=["page"],
    max_results=100
)

# Utilisation
async with ConfluenceClient(config) as client:
    results = await client.search_content(criteria)
    for page in results:
        print(f"Page: {page.title}")
```

📖 **Pour un guide d'utilisation complet avec exemples détaillés**, consultez : **[docs/USAGE_GUIDE.md](docs/USAGE_GUIDE.md)**

Ce guide couvre :

- Configuration étape par étape
- Utilisation programmatique avancée
- Traitement des pièces jointes et conversion markdown
- Optimisations de performance
- Structure de sortie et formats de fichiers

## 🚀 Optimisations de Performance

Le système intègre plusieurs optimisations avancées pour maximiser les performances :

### Pagination Parallèle

Pour les gros volumes de données (>200 résultats), le système utilise automatiquement la pagination parallèle :

```python
# Configuration optimisée pour gros volumes
ENABLE_PARALLEL_PAGINATION=true
MAX_PARALLEL_REQUESTS=3
PARALLEL_PAGINATION_THRESHOLD=200
```

**Avantages** :

- 40-70% d'amélioration des performances pour les gros volumes
- Récupération simultanée de plusieurs pages
- Fallback automatique vers pagination séquentielle en cas d'erreur

### Session HTTP Réutilisable

Le client Confluence utilise une session HTTP réutilisable pour optimiser les connexions :

```python
# Utilisation recommandée avec context manager
async with ConfluenceClient(config) as client:
    results = await client.search_content(criteria)
    # Session fermée automatiquement
```

**Avantages** :

- Réduction de l'overhead de connexion
- Amélioration du débit pour les recherches multiples
- Gestion automatique des ressources

### Configuration de Performance

Ajustez les paramètres selon vos besoins :

| Paramètre                       | Valeur Conservative | Valeur Équilibrée | Valeur Agressive |
| ------------------------------- | ------------------- | ----------------- | ---------------- |
| `MAX_PARALLEL_REQUESTS`         | 2                   | 3                 | 5                |
| `PARALLEL_PAGINATION_THRESHOLD` | 300                 | 200               | 100              |
| `MAX_PARALLEL_DOWNLOADS`        | 3                   | 5                 | 8                |

### Tests de Performance

```bash
# Benchmark de pagination parallèle
python benchmarks/parallel_pagination_benchmark.py

# Tests rapides d'optimisation
python tests/test_parallel_pagination.py
python tests/test_optimized_client.py

# Comparaison des méthodes
python examples/parallel_pagination_example.py
```

## 🏥 Health Checks et Surveillance

Le système intègre un système complet de health checks pour surveiller l'état opérationnel de tous les composants critiques.

### Configuration des Health Checks

Configurez les health checks dans votre fichier `.env` :

```bash
# Activation des health checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# Seuils d'alerte
HEALTH_MEMORY_THRESHOLD=85.0
HEALTH_DISK_THRESHOLD=90.0
HEALTH_ERROR_RATE_THRESHOLD=10.0

# Configuration des checks spécifiques
HEALTH_CHECK_CONFLUENCE=true
HEALTH_CHECK_STORAGE=true
HEALTH_CHECK_CIRCUIT_BREAKERS=true
HEALTH_CHECK_THREAD_POOLS=true
HEALTH_CHECK_SYSTEM_RESOURCES=true
```

### Types de Health Checks

#### 🖥️ Ressources Système

- **Mémoire** : Surveillance de l'utilisation RAM avec seuils configurables
- **Disque** : Monitoring de l'espace disque disponible
- **Performance** : Mesure des temps de réponse

#### 🧵 Thread Pools

- **Utilisation** : Pourcentage d'utilisation des pools de threads
- **Surcharge** : Détection automatique des pools surchargés (>90%)
- **Statistiques** : Threads actifs, taille des queues

#### 🌐 API Confluence

- **Connectivité** : Test de connexion avec timeout configurable
- **Authentification** : Validation des tokens d'accès
- **Latence** : Mesure des temps de réponse

#### 💾 Stockage

- **Lecture/Écriture** : Tests d'accès au stockage
- **Performance** : Mesure des temps d'accès
- **Compatibilité** : Support FileSystem et Google Cloud Storage

#### ⚡ Circuit Breakers

- **État** : Monitoring des états CLOSED/OPEN/HALF_OPEN
- **Taux d'erreur** : Surveillance des échecs et récupération
- **Statistiques** : Compteurs de succès/échecs par service

### Endpoints de Health Check

Le système expose plusieurs endpoints pour différents cas d'usage :

```bash
# Health check complet
curl http://localhost:8000/health

# Readiness check (Kubernetes)
curl http://localhost:8000/ready

# Liveness check (Kubernetes)
curl http://localhost:8000/live

# Health check détaillé (force nouveau check)
curl http://localhost:8000/health/detailed
```

### Réponse Health Check

```json
{
  "overall_status": "healthy",
  "timestamp": "2023-12-07T10:30:00Z",
  "checks": [
    {
      "name": "system_resources",
      "status": "healthy",
      "message": "Ressources normales: Mémoire 45.2%, Disque 23.1%",
      "details": {
        "memory": {
          "used_percent": 45.2,
          "available_gb": 8.7,
          "total_gb": 16.0
        },
        "disk": {
          "used_percent": 23.1,
          "free_gb": 120.5,
          "total_gb": 156.8
        }
      },
      "duration_ms": 12.5
    }
  ],
  "summary": {
    "total_checks": 5,
    "healthy": 4,
    "degraded": 1,
    "unhealthy": 0,
    "unknown": 0
  }
}
```

### États de Santé

- **🟢 healthy** : Tous les composants fonctionnent normalement
- **🟡 degraded** : Fonctionnement avec performance réduite
- **🔴 unhealthy** : Composants critiques en échec
- **⚪ unknown** : État indéterminable

### Intégration Kubernetes

```yaml
# Liveness Probe
livenessProbe:
  httpGet:
    path: /live
    port: 8000
  initialDelaySeconds: 30
  periodSeconds: 10

# Readiness Probe
readinessProbe:
  httpGet:
    path: /ready
    port: 8000
  initialDelaySeconds: 5
  periodSeconds: 5
```

### Utilisation Programmatique

```python
from confluence.health_check import HealthChecker
from confluence.config import get_config

# Configuration
config = get_config()
health_checker = HealthChecker(config.health_check)

# Exécution d'un health check
report = await health_checker.check_system_health(
    confluence_config=config.confluence,
    storage_provider=storage_provider,
    circuit_breaker=circuit_breaker
)

# Analyse du résultat
if report.overall_status.value == "healthy":
    print("✅ Système en bonne santé")
elif report.overall_status.value == "degraded":
    print("⚠️ Système dégradé - surveillance requise")
else:
    print("❌ Système défaillant - intervention nécessaire")
```

### Tests des Health Checks

```bash
# Test complet du système de health checks
python test_health_checks.py

# Test des endpoints via l'API de monitoring
python monitoring_app.py
# Puis tester: curl http://localhost:8000/health
```

Voir [docs/HEALTH_CHECK_GUIDE.md](docs/HEALTH_CHECK_GUIDE.md) pour la documentation complète.

## 🔒 Sécurité

Le système intègre plusieurs mécanismes de sécurité :

- **Authentification sécurisée** : Support des Personal Access Tokens (PAT) et API tokens
- **Protection des logs** : Masquage automatique des tokens et informations sensibles dans les logs
- **Filtrage de sécurité** : Interception et nettoyage de tous les messages de log
- **Validation des entrées** : Vérification des paramètres de configuration
- **Gestion des erreurs** : Exceptions personnalisées avec informations contextuelles nettoyées
- **Timeouts configurables** : Protection contre les requêtes bloquées
- **Retry avec backoff** : Évite la surcharge des serveurs en cas d'erreur

### Sécurité des logs

Le système protège automatiquement les informations sensibles dans les logs :

```python
# Les tokens sont automatiquement masqués
logger.error("Erreur avec token ABC123XYZ456789")
# Résultat: "Erreur avec token ***TOKEN***"

# Les headers d'authentification sont nettoyés
logger.debug("Authorization: Bearer SECRET_TOKEN")
# Résultat: "Authorization: Bearer ***TOKEN***"
```

Voir [docs/securite_logs.md](docs/securite_logs.md) pour plus de détails.

## Tests

### 🔗 Tests d'intégration kbot-load-scheduler

```bash
# Tests du ConfluenceLoader intégré
pytest tests/loader/test_confluence_loader.py -v

# Tests complets du projet avec Confluence
pytest tests/ -v
```

### 📦 Tests du module standalone

```bash
# Tests unitaires
python -m pytest tests/ -v

# Tests de performance
python tests/test_parallel_pagination.py
python tests/test_performance_optimization.py

# Benchmarks
python benchmarks/parallel_pagination_benchmark.py

# Tests d'intégration avec vraie instance Confluence
python tests/test_real_confluence_integration.py
```

## Documentation

Consultez le répertoire `docs/` pour la documentation détaillée :

### 📖 Guides d'Utilisation

- **[Guide d'Utilisation Complet](docs/USAGE_GUIDE.md)** - Guide détaillé pour rechercher et télécharger du contenu Confluence

### 🏗️ Architecture et Traitement

- **[Architecture de Traitement Modulaire](docs/PROCESSING_ARCHITECTURE.md)** - Guide complet de la nouvelle architecture
- **[Guide de Migration du Traitement](docs/PROCESSING_MIGRATION_GUIDE.md)** - Migration vers l'architecture modulaire
- **[API Reference - Traitement](docs/PROCESSING_API_REFERENCE.md)** - Documentation complète de l'API
- **[Guide de Développement - Traitement](docs/PROCESSING_DEVELOPMENT_GUIDE.md)** - Développement et extension des processeurs
- [Guide de pagination parallèle](docs/PARALLEL_PAGINATION_GUIDE.md)
- [Optimisations de performance](docs/PERFORMANCE_OPTIMIZATION.md)
- [Gestion des pools de threads](docs/THREAD_POOL_OPTIMIZATION.md)

### 🏥 Monitoring et Sécurité

- [Guide des Health Checks](docs/HEALTH_CHECK_GUIDE.md)
- [Journalisation structurée](docs/journalisation_structuree.md)
- [Sécurité des logs](docs/securite_logs.md)
- [Circuit Breaker](docs/circuit_breaker.md)

## Exemples

Voir le répertoire `examples/` pour des exemples d'utilisation :

- [Pagination parallèle](examples/parallel_pagination_example.py)
- [Recherche optimisée](examples/optimized_search_example.py)
- [Démonstration sécurité des logs](examples/demo_log_security.py)

## Version

Version actuelle : 1.2.0

## Scripts utilitaires

Le répertoire `scripts/` contient des outils de développement :

## Rapports

Le répertoire `reports/` contient les rapports d'analyse :

## Contribution

Pour contribuer à ce module, veuillez consulter la documentation de développement dans `docs/`.
