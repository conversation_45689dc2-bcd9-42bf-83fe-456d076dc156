{"confluence": {"url": "https://example.atlassian.net", "pat_token": "your_pat_token_here", "default_space_key": "DOCS", "timeout": 45, "retry_config": {"max_retries": 5, "initial_backoff": 1.5, "max_backoff": 120.0, "backoff_factor": 2.5, "jitter": true, "retry_on_status_codes": [429, 500, 502, 503, 504]}, "circuit_breaker_config": {"failure_threshold": 10, "reset_timeout": 120.0, "reset_threshold": 3, "enabled": true}, "enable_parallel_pagination": true, "max_parallel_requests": 5, "parallel_pagination_threshold": 300}, "thread_pool": {"io_thread_workers": 12, "document_processing_workers": 6, "api_thread_workers": 4, "thread_name_prefix": "ProdConfluenceRAG", "max_queue_size": 200}, "logging": {"level": "WARNING", "enable_console": false, "enable_file": true, "file_path": "logs/confluence_rag_prod.log", "max_file_size_mb": 50, "backup_count": 20, "file_encoding": "utf-8", "structured": true, "include_traceback": true, "enable_security_filter": true, "enable_correlation_id": true, "suppress_external_loggers": true, "external_logger_level": "ERROR"}, "debug_mode": false, "enable_profiling": false, "enable_metrics": true}