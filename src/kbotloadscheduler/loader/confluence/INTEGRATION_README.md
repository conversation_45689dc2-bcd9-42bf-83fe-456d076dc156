# Intégration du module Confluence dans kbot-load-scheduler

## Vue d'ensemble

Le module Confluence a été intégré dans l'architecture existante de kbot-load-scheduler en respectant les conventions et patterns du projet.

## Architecture d'intégration

### 1. ConfluenceLoader

Le `ConfluenceLoader` hérite de `AbstractLoader` et implémente l'interface standardisée :

```python
class ConfluenceLoader(AbstractLoader):
    def get_document_list(self, source: SourceBean) -> List[DocumentBean]
    def get_document(self, source: SourceBean, document: DocumentBean, output_path: str) -> Dict[str, Any]
```

### 2. Intégration dans le container de dépendances

Le loader est enregistré dans `src/kbotloadscheduler/dependency/container.py` :

```python
confluence_loader = providers.Factory(ConfluenceLoader, config_with_secret=configWithSecret)

loader_manager = providers.Factory(
    LoaderManager,
    gcs=gcs_loader,
    basic=basic_loader,
    sharepoint=sharepoint_loader,
    confluence=confluence_loader  # Nouveau loader
)
```

### 3. Gestion des secrets

Extension de `ConfigWithSecret` pour supporter les credentials Confluence :

```python
def get_confluence_credentials(self, perimeter_code):
    # Recherche par ordre de priorité :
    # 1. {perimeter_code}-confluence-credentials (JSON)
    # 2. confluence-credentials (JSON global)
    # 3. Clés séparées (legacy)
```

## Configuration

### Variables d'environnement

```bash
# Obligatoire
CONFLUENCE_URL=https://mycompany.atlassian.net

# Optionnel
DEFAULT_SPACE_KEY=DOCS
CONFLUENCE_TIMEOUT=30
```

### Configuration de source

```json
{
  "src_type": "confluence",
  "configuration": {
    "spaces": ["DOCS", "TECH"],
    "max_results": 1000,
    "include_attachments": true,
    "content_types": ["page", "blogpost"],
    "last_modified_days": 30,
    "labels": ["public"],
    "exclude_labels": ["draft"],
    "title_contains": "API"
  }
}
```

### Secrets Manager

#### Format recommandé (JSON)
```json
{
  "pat_token": "your_personal_access_token"
}
```

#### Format legacy (clés séparées)
- `{perimeter}-confluence-pat-token`
- `{perimeter}-confluence-username`
- `{perimeter}-confluence-api-token`

## Utilisation

### Via l'API REST

```bash
# Lister les documents
POST /loader/list/{perimeter_code}
{
  "get_list_file": "path/to/confluence_source_config.json"
}

# Récupérer un document
POST /loader/document/{perimeter_code}
{
  "document_get_file": "path/to/document_config.json"
}
```

### Programmatique

```python
from kbotloadscheduler.loader.confluence_loader import ConfluenceLoader
from kbotloadscheduler.bean.beans import SourceBean

# Créer le loader
loader = ConfluenceLoader(config_with_secret)

# Récupérer la liste des documents
documents = loader.get_document_list(source_bean)

# Récupérer un document spécifique
metadata = loader.get_document(source_bean, document_bean, output_path)
```

## Mapping des données

### ContentItem → DocumentBean

```python
DocumentBean(
    id=f"{domain_code}/{source_code}/page_{confluence_id}",
    name=confluence_title,
    path=confluence_web_ui_link,
    modification_time=confluence_last_modified
)
```

### Métadonnées générées

```python
{
    Metadata.DOCUMENT_ID: document.id,
    Metadata.DOCUMENT_NAME: document.name,
    Metadata.LOCATION: gcs_path,
    Metadata.DOMAIN_CODE: source.domain_code,
    Metadata.SOURCE_CODE: source.code,
    Metadata.SOURCE_TYPE: "confluence",
    Metadata.MODIFICATION_TIME: document.modification_time,
    "confluence_sync_stats": sync_result  # Statistiques spécifiques
}
```

## Fonctionnalités préservées

Toutes les fonctionnalités avancées du module Confluence original sont préservées :

- ✅ **Pagination parallèle** - Optimisation des performances
- ✅ **Circuit breaker** - Résilience aux pannes
- ✅ **Retry logic** - Gestion des erreurs temporaires
- ✅ **Thread pool management** - Gestion optimisée des threads
- ✅ **Health checks** - Monitoring de la santé du système
- ✅ **Logging structuré** - Traçabilité avec correlation IDs
- ✅ **Stockage GCS** - Intégration avec Google Cloud Storage
- ✅ **Processing modulaire** - Architecture en modules spécialisés
- ✅ **Gestion des attachments** - Support des pièces jointes
- ✅ **Extraction de contenu** - Support multi-formats (PDF, Word, etc.)

## Tests

### Tests unitaires

```bash
# Exécuter les tests du ConfluenceLoader
pytest tests/loader/test_confluence_loader.py -v

# Exécuter tous les tests du module confluence
pytest src/kbotloadscheduler/loader/confluence/tests/ -v
```

### Tests d'intégration

```bash
# Test avec une vraie instance Confluence
pytest src/kbotloadscheduler/loader/confluence/tests/test_real_confluence_integration.py -v
```

## Migration depuis l'utilisation standalone

Si vous utilisiez le module Confluence de manière standalone, voici les étapes de migration :

### 1. Configuration des secrets

Migrez vos credentials vers Secret Manager avec les nouvelles clés :

```bash
# Ancien format
confluence-pat-token

# Nouveau format (recommandé)
{perimeter}-confluence-credentials
```

### 2. Configuration de source

Créez une `SourceBean` avec la configuration JSON :

```python
source = SourceBean(
    src_type="confluence",
    configuration=json.dumps({
        "spaces": ["YOUR_SPACE"],
        "max_results": 1000,
        # ... autres options
    }),
    # ... autres champs
)
```

### 3. Utilisation via LoaderManager

```python
# Ancien
orchestrator = SyncOrchestrator(config, criteria)
await orchestrator.run()

# Nouveau
loader = loader_manager.get_loader("confluence")
documents = loader.get_document_list(source)
```

## Troubleshooting

### Erreurs communes

1. **"CONFLUENCE_URL environment variable is required"**
   - Définir la variable d'environnement `CONFLUENCE_URL`

2. **"No valid Confluence credentials found"**
   - Vérifier la configuration des secrets dans Secret Manager
   - S'assurer que le format JSON est valide

3. **"Failed to get document list"**
   - Vérifier les permissions Confluence
   - Contrôler la connectivité réseau
   - Examiner les logs pour plus de détails

### Logs et monitoring

Le module utilise le système de logging structuré avec correlation IDs :

```python
# Les logs incluent automatiquement :
# - correlation_id : Identifiant de traçabilité
# - source_code : Code de la source
# - perimeter_code : Code du périmètre
# - confluence_stats : Statistiques de synchronisation
```

## Évolutions futures

### Fonctionnalités planifiées

- [ ] Support des espaces personnels
- [ ] Synchronisation incrémentale avancée
- [ ] Cache distribué pour les métadonnées
- [ ] Webhooks Confluence pour synchronisation en temps réel
- [ ] Support des templates Confluence

### Optimisations

- [ ] Parallélisation au niveau des espaces
- [ ] Compression des données stockées
- [ ] Indexation des métadonnées
- [ ] Métriques Prometheus

## Support

Pour toute question ou problème :

1. Consulter les logs avec correlation ID
2. Vérifier la configuration des secrets
3. Tester avec les tests d'intégration
4. Consulter la documentation du module Confluence original

## Références

- [Documentation module Confluence](./README.md)
- [Tests d'intégration](./tests/README.md)
- [Configuration avancée](./docs/)
- [Architecture kbot-load-scheduler](../../README.md)
