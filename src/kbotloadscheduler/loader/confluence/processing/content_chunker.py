#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Text chunking functionality for the Confluence RAG system.
"""

from typing import List, Dict, Any

from ..models import ContentItem
from ..utils import TextProcessor


class ContentChunker:
    """Handles text chunking with configurable strategies."""

    def __init__(self, chunk_size: int, overlap_size: int):
        """
        Initialize the chunker with specified parameters.
        
        Args:
            chunk_size: Maximum size of each text chunk
            overlap_size: Number of characters to overlap between chunks
        """
        self.chunk_size = chunk_size
        self.overlap_size = overlap_size

    def create_chunks(self, content_item: ContentItem) -> List[Dict[str, Any]]:
        """
        Create text chunks from content item.
        
        Args:
            content_item: Content item to chunk
            
        Returns:
            List of chunk dictionaries with metadata
        """
        if not content_item.body_plain:
            return []

        chunks = TextProcessor.chunk_text(
            content_item.body_plain,
            chunk_size=self.chunk_size,
            overlap=self.overlap_size
        )

        return [
            {
                "chunk_id": f"{content_item.id}_chunk_{i}",
                "content": chunk,
                "start_index": self._calculate_start_index(i),
                "metadata": self._create_chunk_metadata(content_item)
            }
            for i, chunk in enumerate(chunks)
        ]

    def _calculate_start_index(self, chunk_index: int) -> int:
        """
        Calculate start index for chunk.
        
        Args:
            chunk_index: Index of the chunk
            
        Returns:
            Start character index for the chunk
        """
        return chunk_index * (self.chunk_size - self.overlap_size) if chunk_index > 0 else 0

    def _create_chunk_metadata(self, content_item: ContentItem) -> Dict[str, str]:
        """
        Create metadata for chunks.
        
        Args:
            content_item: Source content item
            
        Returns:
            Metadata dictionary for the chunk
        """
        return {
            "title": content_item.title,
            "space_key": content_item.space.key,
            "content_type": content_item.type,
            "url": content_item.web_ui_url
        }
