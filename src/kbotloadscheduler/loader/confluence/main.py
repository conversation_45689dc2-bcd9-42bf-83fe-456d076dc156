#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Module principal pour le système RAG Confluence.
"""

import os
import sys
import logging
import asyncio
import json
from logging.handlers import RotatingFileHandler
from typing import Optional, Dict, Any, Union

from .config import ConfluenceConfig, SearchCriteria, StorageConfig, ProcessingConfig, LoggingConfig
from .orchestrator import SyncOrchestrator
from .logging_utils import CorrelationIdFilter, StructuredLogFormatter, SecurityFilter, with_correlation_id, CorrelationContext


def setup_logging(config: LoggingConfig = None) -> None:
    """
    Configure le système de logging à partir d'un objet LoggingConfig.

    Args:
        config: Configuration de logging. Si None, utilise la configuration par défaut.
    """
    if config is None:
        config = LoggingConfig()

    # Déterminer le niveau de log numérique
    numeric_level = getattr(logging, config.level.upper(), None)
    if not isinstance(numeric_level, int):
        numeric_level = logging.INFO

    # Créer les filtres selon la configuration
    filters = []
    if config.enable_security_filter:
        filters.append(SecurityFilter())
    if config.enable_correlation_id:
        filters.append(CorrelationIdFilter())

    # Configurer le formateur selon le format choisi
    if config.structured:
        # Format JSON structuré
        formatter = StructuredLogFormatter(include_traceback=config.include_traceback)
    else:
        # Format texte classique avec identifiant de corrélation
        if config.enable_correlation_id:
            log_format = "%(asctime)s - %(name)s - %(levelname)s - [%(correlation_id)s] - %(message)s"
        else:
            log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        date_format = "%Y-%m-%d %H:%M:%S"
        formatter = logging.Formatter(log_format, date_format)

    # Configurer le logger racine
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)

    # Supprimer les handlers existants pour éviter les doublons
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Configurer le handler de console si activé
    if config.enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        for filter_obj in filters:
            console_handler.addFilter(filter_obj)
        root_logger.addHandler(console_handler)

    # Configurer le handler de fichier si activé
    if config.enable_file:
        # Créer le répertoire parent si nécessaire
        file_dir = os.path.dirname(os.path.abspath(config.file_path))
        if file_dir and not os.path.exists(file_dir):
            os.makedirs(file_dir, exist_ok=True)

        file_handler = RotatingFileHandler(
            config.file_path,
            maxBytes=config.max_file_size_mb * 1024 * 1024,
            backupCount=config.backup_count,
            encoding=config.file_encoding
        )
        file_handler.setFormatter(formatter)
        for filter_obj in filters:
            file_handler.addFilter(filter_obj)
        root_logger.addHandler(file_handler)

    # Configurer les loggers externes si demandé
    if config.suppress_external_loggers:
        external_level = getattr(logging, config.external_logger_level.upper(), logging.WARNING)
        logging.getLogger("urllib3").setLevel(external_level)
        logging.getLogger("requests").setLevel(external_level)
        logging.getLogger("atlassian").setLevel(external_level)

    # Log de confirmation
    handlers_info = []
    if config.enable_console:
        handlers_info.append("console")
    if config.enable_file:
        handlers_info.append(f"fichier ({config.file_path})")

    logging.info(
        f"Logging configuré au niveau {config.level} avec format "
        f"{'structuré' if config.structured else 'standard'}, "
        f"handlers: {', '.join(handlers_info)}, "
        f"sécurisation: {'activée' if config.enable_security_filter else 'désactivée'}"
    )


def setup_logging_legacy(log_level: str = None, log_file: str = None, structured: bool = None) -> None:
    """
    Configure le système de logging (version legacy pour compatibilité).

    DEPRECATED: Utilisez setup_logging(LoggingConfig) à la place.

    Args:
        log_level: Niveau de log (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Chemin du fichier de log
        structured: Si True, utilise le format JSON structuré pour les logs
    """
    # Créer une configuration à partir des paramètres legacy
    config = LoggingConfig()

    if log_level is not None:
        config.level = log_level.upper()
    else:
        config.level = os.getenv("LOG_LEVEL", "INFO").upper()

    if log_file is not None:
        config.file_path = log_file
    else:
        config.file_path = os.getenv("LOG_FILE", "confluence_rag.log")

    if structured is not None:
        config.structured = structured
    else:
        config.structured = os.getenv("STRUCTURED_LOGGING", "true").lower() == "true"

    # Utiliser la nouvelle fonction
    setup_logging(config)


@with_correlation_id
async def amain(
    config: ConfluenceConfig,
    criteria: SearchCriteria,
    storage_config: StorageConfig = None,
    processing_config: ProcessingConfig = None,
    logging_config: LoggingConfig = None
) -> None:
    """Fonction principale asynchrone."""
    # Configurer le logging
    if logging_config is None:
        logging_config = LoggingConfig()
    setup_logging(logging_config)

    # Récupérer l'identifiant de corrélation généré par le décorateur
    correlation_id = CorrelationContext.get_correlation_id()
    logging.info(f"Démarrage du processus de synchronisation avec l'identifiant de corrélation: {correlation_id}")

    # Utiliser les configurations par défaut si non spécifiées
    if storage_config is None:
        storage_config = StorageConfig.from_env()

    if processing_config is None:
        processing_config = ProcessingConfig.from_env()

    # Créer et exécuter l'orchestrateur
    orchestrator = SyncOrchestrator(
        config,
        criteria,
        storage_config,
        processing_config
    )

    # Ajouter l'identifiant de corrélation aux statistiques
    orchestrator.stats["correlation_id"] = correlation_id

    await orchestrator.run()


if __name__ == "__main__":
    # Ce code ne sera pas exécuté lorsque le module est importé
    # Il est destiné à être utilisé pour des tests rapides
    from dotenv import load_dotenv

    # Charger les variables d'environnement
    load_dotenv()

    # Créer une configuration de test
    config_params = {
        "url": os.getenv("CONFLUENCE_URL"),
        "default_space_key": os.getenv("DEFAULT_SPACE_KEY", "EXAMPLE")
    }

    # Ajouter les paramètres d'authentification selon la méthode disponible
    if os.getenv("CONFLUENCE_PAT_TOKEN"):
        # Méthode 1: Personal Access Token (PAT)
        config_params["pat_token"] = os.getenv("CONFLUENCE_PAT_TOKEN")
    elif os.getenv("CONFLUENCE_USERNAME") and os.getenv("CONFLUENCE_API_TOKEN"):
        # Méthode 2: API Token classique
        config_params["username"] = os.getenv("CONFLUENCE_USERNAME")
        config_params["api_token"] = os.getenv("CONFLUENCE_API_TOKEN")

    test_config = ConfluenceConfig(**config_params)

    # Créer des critères de recherche de test
    # Essayer de charger depuis le fichier (local ou GCS), sinon utiliser des valeurs par défaut
    criteria_file = os.getenv("CRITERIA_FILE_PATH", "criteres_recherche.json")
    gcs_bucket = os.getenv("CRITERIA_GCS_BUCKET")

    # Vérifier si le chemin du fichier est une URL GCS (gs://bucket/path)
    if criteria_file.startswith("gs://") and not gcs_bucket:
        # Extraire le bucket et le chemin de l'URL GCS
        gcs_path = criteria_file[5:]  # Supprimer le préfixe "gs://"
        bucket_end = gcs_path.find("/")
        if bucket_end > 0:
            gcs_bucket = gcs_path[:bucket_end]
            criteria_file = gcs_path[bucket_end+1:]

    try:
        test_criteria = SearchCriteria.from_file(criteria_file, gcs_bucket)
        if not test_criteria.spaces:
            test_criteria.spaces = [test_config.default_space_key]
    except Exception as e:
        print(f"Erreur lors du chargement des critères: {e}")
        test_criteria = SearchCriteria(
            spaces=[test_config.default_space_key],
            max_results=10
        )

    # Créer les configurations de test
    test_storage_config = StorageConfig.from_env()
    test_processing_config = ProcessingConfig.from_env()

    # Exécuter la fonction principale
    asyncio.run(amain(
        test_config,
        test_criteria,
        test_storage_config,
        test_processing_config
    ))