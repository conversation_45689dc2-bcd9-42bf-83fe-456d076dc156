from datetime import datetime, timezone
from unittest.mock import MagicMock, patch

import pytest
from testutils.mock_gcs import MockGcs

from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
from kbotloadscheduler.loader.confluence_loader import ConfluenceLoader, LoaderException


class TestConfluenceLoader:
    """Tests pour le ConfluenceLoader"""

    @pytest.fixture
    def mock_config_with_secret(self):
        """Mock du ConfigWithSecret"""
        config_mock = MagicMock()
        config_mock.get_confluence_credentials.return_value = {
            "pat_token": "test_pat_token",
            "username": "test_user",
            "api_token": "test_api_token"
        }
        return config_mock

    @pytest.fixture
    def source_bean(self):
        """Source bean de test pour Confluence"""
        return SourceBean(
            id=1,
            code="test_confluence",
            label="Test Confluence Source",
            src_type="confluence",
            configuration='{"spaces": ["TEST"], "max_results": 100, "include_attachments": true}',
            last_load_time=1234567890,
            load_interval=24,
            domain_code="test_domain",
            perimeter_code="test_perimeter",
            force_embedding=False
        )

    @pytest.fixture
    def mock_content_item(self):
        """Mock d'un ContentItem Confluence"""
        content_item = MagicMock()
        content_item.id = "123456"
        content_item.title = "Test Page"
        content_item.web_ui_link = "https://confluence.example.com/display/TEST/Test+Page"
        content_item.last_modified = datetime.now(timezone.utc)
        content_item.attachments = []
        return content_item

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST',
        'CONFLUENCE_TIMEOUT': '30'
    })
    def test_init_success(self, mock_config_with_secret):
        """Test de l'initialisation réussie du ConfluenceLoader"""
        loader = ConfluenceLoader(mock_config_with_secret)

        assert loader._loader_type == "confluence"
        assert loader.confluence_url == "https://confluence.example.com"
        assert loader.default_space_key == "TEST"

    def test_init_missing_url(self, mock_config_with_secret):
        """Test de l'initialisation avec URL manquante"""
        with patch.dict('os.environ', {}, clear=True):
            with pytest.raises(LoaderException, match="CONFLUENCE_URL environment variable is required"):
                ConfluenceLoader(mock_config_with_secret)

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_create_confluence_config_with_pat(self, mock_config_with_secret, source_bean):
        """Test de création de configuration avec PAT token"""
        mock_config_with_secret.get_confluence_credentials.return_value = {
            "pat_token": "test_pat_token"
        }

        loader = ConfluenceLoader(mock_config_with_secret)
        config = loader._create_confluence_config(source_bean)

        assert str(config.url).rstrip('/') == "https://confluence.example.com"
        assert config.default_space_key == "TEST"
        assert config.pat_token.get_secret_value() == "test_pat_token"

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_create_confluence_config_with_api_token(self, mock_config_with_secret, source_bean):
        """Test de création de configuration avec API token"""
        mock_config_with_secret.get_confluence_credentials.return_value = {
            "username": "test_user",
            "api_token": "test_api_token"
        }

        loader = ConfluenceLoader(mock_config_with_secret)
        config = loader._create_confluence_config(source_bean)

        assert str(config.url).rstrip('/') == "https://confluence.example.com"
        assert config.username == "test_user"
        assert config.api_token.get_secret_value() == "test_api_token"

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_create_confluence_config_no_credentials(self, mock_config_with_secret, source_bean):
        """Test de création de configuration sans credentials valides"""
        mock_config_with_secret.get_confluence_credentials.return_value = {}

        loader = ConfluenceLoader(mock_config_with_secret)

        with pytest.raises(LoaderException, match="No valid Confluence credentials found"):
            loader._create_confluence_config(source_bean)

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_create_search_criteria(self, mock_config_with_secret, source_bean):
        """Test de création des critères de recherche"""
        loader = ConfluenceLoader(mock_config_with_secret)
        criteria = loader._create_search_criteria(source_bean)

        assert criteria.spaces == ["TEST"]
        assert criteria.max_results == 100
        assert criteria.include_attachments is True

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_content_item_to_document_bean(self, mock_config_with_secret, source_bean, mock_content_item):
        """Test de conversion ContentItem vers DocumentBean"""
        loader = ConfluenceLoader(mock_config_with_secret)
        doc_bean = loader._content_item_to_document_bean(source_bean, mock_content_item)

        assert doc_bean.id == "test_domain/test_confluence/page_123456"
        assert doc_bean.name == "Test Page"
        assert doc_bean.path == "https://confluence.example.com/display/TEST/Test+Page"
        assert doc_bean.modification_time == mock_content_item.last_modified

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_extract_bucket_from_path(self, mock_config_with_secret):
        """Test d'extraction du bucket depuis un chemin GCS"""
        loader = ConfluenceLoader(mock_config_with_secret)

        # Test avec chemin gs://
        bucket = loader._extract_bucket_from_path("gs://test-bucket/path/to/file")
        assert bucket == "test-bucket"

        # Test sans gs://
        bucket = loader._extract_bucket_from_path("test-bucket/path/to/file")
        assert bucket == "test-bucket"

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_extract_prefix_from_path(self, mock_config_with_secret):
        """Test d'extraction du préfixe depuis un chemin GCS"""
        loader = ConfluenceLoader(mock_config_with_secret)

        # Test avec chemin gs://
        prefix = loader._extract_prefix_from_path("gs://test-bucket/path/to/file")
        assert prefix == "path/to/file"

        # Test sans gs://
        prefix = loader._extract_prefix_from_path("test-bucket/path/to/file")
        assert prefix == "path/to/file"

        # Test avec bucket seulement
        prefix = loader._extract_prefix_from_path("gs://test-bucket")
        assert prefix == ""

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_get_document_list_integration_test(self, mock_config_with_secret, source_bean):
        """Test d'intégration - vérifie que la méthode fonctionne (retourne une liste vide sans vraie instance)"""
        loader = ConfluenceLoader(mock_config_with_secret)

        # Test que la méthode fonctionne et retourne une liste (vide sans vraie instance Confluence)
        documents = loader.get_document_list(source_bean)
        assert isinstance(documents, list)
        # Sans vraie instance Confluence, on s'attend à 0 documents
        assert len(documents) == 0

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_get_document_integration_test(self, mock_config_with_secret, source_bean):
        """Test d'intégration simplifié pour get_document"""
        loader = ConfluenceLoader(mock_config_with_secret)

        document = DocumentBean(
            id="test_doc",
            name="Test Document",
            path="test/path",
            modification_time=datetime.now(timezone.utc)
        )

        # Test que la méthode existe et peut être appelée (même si elle échoue)
        with pytest.raises(LoaderException):
            loader.get_document(source_bean, document, "gs://test-bucket/test-path")

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_get_document_with_mock_gcs(self, mocker, mock_config_with_secret, source_bean):
        """Test de get_document avec MockGCS - test simplifié"""
        # Setup MockGCS
        mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        test_bucket = "test-bucket"
        test_prefix = "test-path"
        mock_gcs.add_bucket(test_bucket)

        # Créer le loader
        loader = ConfluenceLoader(mock_config_with_secret)

        # Créer un document de test
        document = DocumentBean(
            id="test_domain/test_source/page_123456",
            name="Test Document",
            path="https://confluence.example.com/pages/123456",
            modification_time=datetime.now(timezone.utc)
        )

        # Test que MockGCS est bien configuré
        assert test_bucket in mock_gcs.buckets

        # Test des méthodes utilitaires avec MockGCS
        output_path = f"gs://{test_bucket}/{test_prefix}"
        extracted_bucket = loader._extract_bucket_from_path(output_path)
        extracted_prefix = loader._extract_prefix_from_path(output_path)

        assert extracted_bucket == test_bucket
        assert extracted_prefix == test_prefix

        # Test que get_document peut être appelé (même si il échoue sans vraie instance)
        # Ceci valide que MockGCS n'interfère pas avec l'interface du loader
        with pytest.raises(LoaderException):
            loader.get_document(source_bean, document, output_path)
