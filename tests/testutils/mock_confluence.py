"""
Mock Confluence pour les tests.

Ce module fournit un mock complet de Confluence qui simule :
- L'API REST Confluence
- Les réponses de recherche de contenu
- Les pages et attachments
- Les espaces Confluence
"""

import json
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from unittest.mock import MagicMock, AsyncMock


class MockConfluenceContent:
    """Mock d'un contenu Confluence (page, blogpost, etc.)"""
    
    def __init__(self, content_id: str, title: str, content_type: str = "page", 
                 space_key: str = "TEST", body_content: str = "Mock content"):
        self.id = content_id
        self.title = title
        self.type = content_type
        self.space_key = space_key
        self.body_content = body_content
        self.last_modified = datetime.now(timezone.utc)
        self.created_date = datetime.now(timezone.utc)
        self.web_ui_link = f"https://mock-confluence.example.com/display/{space_key}/{title.replace(' ', '+')}"
        self.attachments = []
        self.labels = []
        
    def to_api_response(self) -> Dict[str, Any]:
        """Convertit en réponse API Confluence"""
        return {
            "id": self.id,
            "title": self.title,
            "type": self.type,
            "space": {"key": self.space_key},
            "body": {
                "storage": {"value": self.body_content},
                "view": {"value": f"<p>{self.body_content}</p>"}
            },
            "version": {"when": self.last_modified.isoformat()},
            "_links": {
                "webui": self.web_ui_link,
                "self": f"https://mock-confluence.example.com/rest/api/content/{self.id}"
            },
            "metadata": {
                "labels": {"results": [{"name": label} for label in self.labels]}
            },
            "children": {
                "attachment": {
                    "results": [att.to_api_response() for att in self.attachments]
                }
            }
        }


class MockConfluenceAttachment:
    """Mock d'un attachment Confluence"""
    
    def __init__(self, attachment_id: str, title: str, media_type: str = "application/pdf"):
        self.id = attachment_id
        self.title = title
        self.media_type = media_type
        self.created_date = datetime.now(timezone.utc)
        self.download_link = f"https://mock-confluence.example.com/download/attachments/{attachment_id}/{title}"
        
    def to_api_response(self) -> Dict[str, Any]:
        """Convertit en réponse API Confluence"""
        return {
            "id": self.id,
            "title": self.title,
            "type": "attachment",
            "metadata": {
                "mediaType": self.media_type
            },
            "version": {"when": self.created_date.isoformat()},
            "_links": {
                "download": self.download_link
            }
        }


class MockConfluenceSpace:
    """Mock d'un espace Confluence"""
    
    def __init__(self, key: str, name: str):
        self.key = key
        self.name = name
        self.contents: List[MockConfluenceContent] = []
        
    def add_content(self, content: MockConfluenceContent):
        """Ajoute du contenu à l'espace"""
        content.space_key = self.key
        self.contents.append(content)
        
    def to_api_response(self) -> Dict[str, Any]:
        """Convertit en réponse API Confluence"""
        return {
            "key": self.key,
            "name": self.name,
            "type": "global"
        }


class MockConfluence:
    """Mock complet de Confluence"""
    
    def __init__(self, base_url: str = "https://mock-confluence.example.com"):
        self.base_url = base_url
        self.spaces: Dict[str, MockConfluenceSpace] = {}
        self.contents: Dict[str, MockConfluenceContent] = {}
        self.request_count = 0
        self.search_history = []
        
    def add_space(self, space_key: str, space_name: str) -> MockConfluenceSpace:
        """Ajoute un espace Confluence"""
        space = MockConfluenceSpace(space_key, space_name)
        self.spaces[space_key] = space
        return space
        
    def add_content(self, space_key: str, content_id: str, title: str, 
                   content_type: str = "page", body_content: str = "Mock content") -> MockConfluenceContent:
        """Ajoute du contenu à un espace"""
        if space_key not in self.spaces:
            self.add_space(space_key, f"Space {space_key}")
            
        content = MockConfluenceContent(content_id, title, content_type, space_key, body_content)
        self.contents[content_id] = content
        self.spaces[space_key].add_content(content)
        return content
        
    def add_attachment(self, content_id: str, attachment_id: str, title: str, 
                      media_type: str = "application/pdf") -> MockConfluenceAttachment:
        """Ajoute un attachment à un contenu"""
        if content_id in self.contents:
            attachment = MockConfluenceAttachment(attachment_id, title, media_type)
            self.contents[content_id].attachments.append(attachment)
            return attachment
        raise ValueError(f"Content {content_id} not found")
        
    def search_content(self, spaces: List[str] = None, content_types: List[str] = None,
                      max_results: int = 1000, labels: List[str] = None,
                      title_contains: str = None, last_modified_days: int = None) -> List[Dict[str, Any]]:
        """Simule une recherche de contenu"""
        self.request_count += 1
        search_params = {
            "spaces": spaces or [],
            "content_types": content_types or ["page", "blogpost"],
            "max_results": max_results,
            "labels": labels or [],
            "title_contains": title_contains,
            "last_modified_days": last_modified_days
        }
        self.search_history.append(search_params)
        
        results = []
        for content in self.contents.values():
            # Filtrer par espace
            if spaces and content.space_key not in spaces:
                continue
                
            # Filtrer par type de contenu
            if content_types and content.type not in content_types:
                continue
                
            # Filtrer par titre
            if title_contains and title_contains.lower() not in content.title.lower():
                continue
                
            # Filtrer par labels
            if labels and not any(label in content.labels for label in labels):
                continue
                
            results.append(content.to_api_response())
            
            # Limiter les résultats
            if len(results) >= max_results:
                break
                
        return results
        
    def get_content_by_id(self, content_id: str) -> Optional[Dict[str, Any]]:
        """Récupère un contenu par son ID"""
        self.request_count += 1
        if content_id in self.contents:
            return self.contents[content_id].to_api_response()
        return None
        
    def get_spaces(self) -> List[Dict[str, Any]]:
        """Récupère la liste des espaces"""
        self.request_count += 1
        return [space.to_api_response() for space in self.spaces.values()]
        
    def get_stats(self) -> Dict[str, Any]:
        """Récupère les statistiques du mock"""
        return {
            "total_spaces": len(self.spaces),
            "total_contents": len(self.contents),
            "total_attachments": sum(len(content.attachments) for content in self.contents.values()),
            "request_count": self.request_count,
            "search_count": len(self.search_history)
        }


def create_mock_confluence_client(mock_confluence: MockConfluence, mocker):
    """Crée un mock du client Confluence qui utilise MockConfluence"""
    
    # Mock du client HTTP
    mock_client = MagicMock()
    
    # Mock des méthodes de recherche
    async def mock_search_content(search_criteria):
        """Mock de la recherche de contenu"""
        results = mock_confluence.search_content(
            spaces=search_criteria.spaces,
            content_types=search_criteria.content_types,
            max_results=search_criteria.max_results,
            labels=getattr(search_criteria, 'labels', []),
            title_contains=getattr(search_criteria, 'title_contains', None),
            last_modified_days=getattr(search_criteria, 'last_modified_days', None)
        )
        
        # Convertir en objets ContentItem mockés
        content_items = []
        for result in results:
            content_item = MagicMock()
            content_item.id = result["id"]
            content_item.title = result["title"]
            content_item.type = result["type"]
            content_item.space = MagicMock()
            content_item.space.key = result["space"]["key"]
            content_item.web_ui_link = result["_links"]["webui"]
            content_item.last_modified = datetime.fromisoformat(result["version"]["when"].replace('Z', '+00:00'))
            
            # Mock des attachments
            content_item.attachments = []
            for att_data in result["children"]["attachment"]["results"]:
                attachment = MagicMock()
                attachment.id = att_data["id"]
                attachment.title = att_data["title"]
                attachment.download_link = att_data["_links"]["download"]
                attachment.created_date = datetime.fromisoformat(att_data["version"]["when"].replace('Z', '+00:00'))
                content_item.attachments.append(attachment)
                
            content_items.append(content_item)
            
        return content_items
    
    mock_client.search_content = AsyncMock(side_effect=mock_search_content)
    
    # Mock des autres méthodes
    async def mock_get_content_by_id(content_id):
        result = mock_confluence.get_content_by_id(content_id)
        if result:
            content_item = MagicMock()
            content_item.id = result["id"]
            content_item.title = result["title"]
            content_item.type = result["type"]
            return content_item
        return None
    
    mock_client.get_content_by_id = AsyncMock(side_effect=mock_get_content_by_id)
    
    return mock_client


def setup_sample_confluence_data(mock_confluence: MockConfluence):
    """Configure des données d'exemple dans MockConfluence"""
    
    # Ajouter des espaces
    docs_space = mock_confluence.add_space("DOCS", "Documentation")
    tech_space = mock_confluence.add_space("TECH", "Technical")
    
    # Ajouter du contenu dans DOCS
    api_guide = mock_confluence.add_content("DOCS", "123456", "API Guide", "page", 
                                          "Complete guide for using our API")
    api_guide.labels = ["public", "api"]
    
    user_manual = mock_confluence.add_content("DOCS", "123457", "User Manual", "page",
                                            "How to use the application")
    user_manual.labels = ["public", "manual"]
    
    # Ajouter des attachments
    mock_confluence.add_attachment("123456", "att001", "api-schema.pdf", "application/pdf")
    mock_confluence.add_attachment("123456", "att002", "examples.zip", "application/zip")
    
    # Ajouter du contenu dans TECH
    architecture = mock_confluence.add_content("TECH", "789012", "System Architecture", "page",
                                             "Technical architecture overview")
    architecture.labels = ["technical", "architecture"]
    
    blog_post = mock_confluence.add_content("TECH", "789013", "New Features", "blogpost",
                                          "Latest features and improvements")
    blog_post.labels = ["news", "features"]
    
    return mock_confluence
