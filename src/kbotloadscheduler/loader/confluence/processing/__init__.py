#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Confluence RAG Processing Package

This package provides modular, focused components for processing Confluence content:

- enums: Processing status and media type enumerations
- document_extractors: Text extraction from various document formats
- drawio_processor: Specialized processing for draw.io diagrams
- attachment_processor: Attachment download and processing
- content_chunker: Text chunking functionality
- content_retriever: Content retrieval and orchestration

This is the new modular architecture that replaces the monolithic processing.py file.
"""

# Import all public classes and enums
from .enums import (
    ProcessingStatus,
    MediaType,
    ExtractionResult,
    DrawIOMetadata
)

from .document_extractors import DocumentExtractor
from .drawio_processor import DrawIOProcessor
from .attachment_processor import AttachmentProcessor
from .content_chunker import ContentChunker
from .content_retriever import ContentRetriever

# Public API
__all__ = [
    # Enums and data classes
    'ProcessingStatus',
    'MediaType',
    'ExtractionResult',
    'DrawIOMetadata',
    
    # Processing components
    'DocumentExtractor',
    'DrawIOProcessor',
    'AttachmentProcessor',
    'ContentChunker',
    'ContentRetriever',
]

# Version info
__version__ = '2.0.0'
__author__ = 'Confluence RAG Team'
__description__ = 'Modular processing components for Confluence RAG system'
