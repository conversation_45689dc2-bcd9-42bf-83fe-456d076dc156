#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests de performance pour comparer l'ancienne et la nouvelle approche de search_content().
"""

import asyncio
import time
import pytest
import logging
from unittest.mock import AsyncMock, MagicMock, patch
from pydantic import SecretStr
from confluence.client import ConfluenceClient
from confluence.config import ConfluenceConfig, SearchCriteria

# Configuration du logging pour les tests
logging.basicConfig(level=logging.INFO)

class TestPerformanceOptimization:
    """Tests de performance pour les optimisations du client Confluence."""

    @pytest.fixture
    def mock_config(self):
        """Configuration mock pour les tests."""
        config = MagicMock(spec=ConfluenceConfig)
        config.url = "https://test.atlassian.net"
        config.pat_token = SecretStr("test_token")
        config.api_token = None
        config.username = None
        config.timeout = 30
        config.retry_config = MagicMock()
        config.circuit_breaker_config = MagicMock()
        return config

    @pytest.fixture
    def search_criteria(self):
        """Critères de recherche pour les tests."""
        return SearchCriteria(
            spaces=["TEST"],
            types=["page"],
            max_results=50
        )

    @pytest.fixture
    def mock_response_data(self):
        """Données de réponse mock pour les tests."""
        return {
            "results": [
                {
                    "id": f"test_id_{i}",
                    "type": "page",
                    "status": "current",
                    "title": f"Test Page {i}",
                    "space": {"id": "1", "key": "TEST", "name": "Test Space", "type": "global"},
                    "version": {"number": 1},
                    "history": {
                        "createdDate": "2023-01-01T00:00:00.000Z",
                        "createdBy": {"accountId": "user1", "displayName": "Test User"},
                        "lastUpdated": {
                            "when": "2023-01-01T00:00:00.000Z",
                            "by": {"accountId": "user1", "displayName": "Test User"}
                        }
                    },
                    "body": {
                        "storage": {"value": f"<p>Content {i}</p>"},
                        "view": {"value": f"<p>Content {i}</p>"}
                    },
                    "metadata": {"labels": {"results": []}},
                    "ancestors": [],
                    "children": {"page": {"results": []}}
                }
                for i in range(10)
            ],
            "size": 10,
            "start": 0,
            "limit": 10
        }

    @pytest.mark.asyncio
    async def test_optimized_session_creation(self, mock_config):
        """Test que la session HTTP est créée correctement."""
        client = ConfluenceClient(mock_config)

        # Vérifier que la session n'est pas créée à l'initialisation
        assert client._session is None

        # Créer la session
        session = await client._get_session()

        # Vérifier que la session est créée avec les bons paramètres
        assert session is not None
        assert not session.closed
        assert "Authorization" in session._default_headers
        assert session._default_headers["Authorization"] == "Bearer test_token"

        # Nettoyer
        await client.close()

    @pytest.mark.asyncio
    async def test_session_reuse(self, mock_config):
        """Test que la session HTTP est réutilisée entre les appels."""
        client = ConfluenceClient(mock_config)

        # Obtenir la session deux fois
        session1 = await client._get_session()
        session2 = await client._get_session()

        # Vérifier que c'est la même instance
        assert session1 is session2

        # Nettoyer
        await client.close()

    @pytest.mark.asyncio
    async def test_context_manager_cleanup(self, mock_config):
        """Test que le context manager ferme proprement la session."""
        async with ConfluenceClient(mock_config) as client:
            session = await client._get_session()
            assert not session.closed

        # Après la sortie du context manager, la session doit être fermée
        assert session.closed

    @pytest.mark.asyncio
    @patch('confluence_rag.client.ConfluenceClient._make_api_request')
    async def test_optimized_search_content(self, mock_api_request, mock_config, search_criteria, mock_response_data):
        """Test de la méthode search_content optimisée."""
        # Configurer le mock pour retourner directement les données JSON
        mock_api_request.return_value = mock_response_data

        async with ConfluenceClient(mock_config) as client:
            # Mesurer le temps d'exécution
            start_time = time.time()
            results = await client.search_content(search_criteria)
            end_time = time.time()

            # Vérifier les résultats
            assert len(results) == 10
            assert all(result.id.startswith("test_id_") for result in results)

            # Vérifier que l'appel API a été fait
            mock_api_request.assert_called()

            # Log du temps d'exécution pour analyse
            execution_time = end_time - start_time
            logging.info(f"Temps d'exécution de la recherche optimisée: {execution_time:.4f}s")

    @pytest.mark.asyncio
    @patch('confluence_rag.client.ConfluenceClient._make_api_request')
    async def test_multiple_searches_performance(self, mock_api_request, mock_config, mock_response_data):
        """Test de performance avec plusieurs recherches consécutives."""
        # Configurer le mock pour retourner directement les données JSON
        mock_api_request.return_value = mock_response_data

        async with ConfluenceClient(mock_config) as client:
            # Effectuer plusieurs recherches
            search_criteria_list = [
                SearchCriteria(spaces=["TEST1"], max_results=10),
                SearchCriteria(spaces=["TEST2"], max_results=10),
                SearchCriteria(spaces=["TEST3"], max_results=10),
            ]

            start_time = time.time()

            all_results = []
            for criteria in search_criteria_list:
                results = await client.search_content(criteria)
                all_results.extend(results)

            end_time = time.time()
            execution_time = end_time - start_time

            # Vérifier les résultats
            assert len(all_results) == 30  # 3 recherches × 10 résultats

            # Vérifier que l'API a été appelée plusieurs fois
            assert mock_api_request.call_count >= 3

            logging.info(f"Temps d'exécution pour 3 recherches: {execution_time:.4f}s")

    @pytest.mark.asyncio
    @patch('confluence_rag.client.ConfluenceClient._fetch_page_parallel')
    async def test_error_handling_in_optimized_method(self, mock_fetch_page, mock_config, search_criteria):
        """Test de la gestion d'erreurs dans la méthode optimisée."""
        from confluence.exceptions import AuthenticationError

        # Simuler une erreur d'authentification lors de la récupération des pages
        mock_fetch_page.side_effect = AuthenticationError("Erreur d'authentification")

        async with ConfluenceClient(mock_config) as client:
            # L'erreur devrait être capturée dans _process_parallel_results mais pas propagée
            # car search_content gère les erreurs de page individuelles
            results = await client.search_content(search_criteria)
            # Vérifier que la liste est vide car toutes les pages ont échoué
            assert len(results) == 0

    @pytest.mark.asyncio
    @patch('confluence_rag.client.ConfluenceClient._fetch_page_parallel')
    async def test_rate_limit_handling(self, mock_fetch_page, mock_config, search_criteria):
        """Test de la gestion des limites de taux."""
        from confluence.exceptions import RateLimitExceededError

        # Simuler une erreur de limite de taux lors de la récupération des pages
        mock_fetch_page.side_effect = RateLimitExceededError(
            "Limite de taux dépassée",
            retry_after=60.0
        )

        async with ConfluenceClient(mock_config) as client:
            # L'erreur devrait être capturée dans _process_parallel_results mais pas propagée
            # car search_content gère les erreurs de page individuelles
            results = await client.search_content(search_criteria)
            # Vérifier que la liste est vide car toutes les pages ont échoué
            assert len(results) == 0

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
