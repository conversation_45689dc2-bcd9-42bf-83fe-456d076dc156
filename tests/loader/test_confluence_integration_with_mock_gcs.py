"""
Tests d'intégration du ConfluenceLoader avec MockGCS.

Ces tests utilisent le MockGCS du projet pour simuler Google Cloud Storage
et tester l'intégration complète du ConfluenceLoader dans l'architecture.
"""

import json
import os
from datetime import datetime, timezone
from unittest.mock import MagicMock, patch

import pytest
from testutils.mock_gcs import MockGcs

from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
from kbotloadscheduler.loader.confluence_loader import ConfluenceLoader


class TestConfluenceIntegrationWithMockGCS:
    """Tests d'intégration du ConfluenceLoader avec MockGCS"""

    @pytest.fixture
    def mock_config_with_secret(self):
        """Mock du ConfigWithSecret"""
        config_mock = MagicMock()
        config_mock.get_confluence_credentials.return_value = {
            "pat_token": "test_pat_token_for_integration"
        }
        return config_mock

    @pytest.fixture
    def confluence_source(self):
        """Source Confluence pour les tests d'intégration"""
        config = {
            "spaces": ["DOCS", "TECH"],
            "max_results": 50,
            "include_attachments": True,
            "content_types": ["page", "blogpost"],
            "last_modified_days": 7,
            "labels": ["public"]
        }

        return SourceBean(
            id=1,
            code="confluence_integration_test",
            label="Confluence Integration Test",
            src_type="confluence",
            configuration=json.dumps(config),
            last_load_time=int(datetime.now(timezone.utc).timestamp()),
            load_interval=24,
            domain_code="integration_domain",
            perimeter_code="integration_test",
            force_embedding=False
        )

    @pytest.fixture
    def test_document(self):
        """Document de test pour les tests d'intégration"""
        return DocumentBean(
            id="integration_domain/confluence_integration_test/page_987654",
            name="Integration Test Document",
            path="https://confluence.example.com/display/DOCS/Integration+Test",
            modification_time=datetime.now(timezone.utc)
        )

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence-integration.example.com',
        'DEFAULT_SPACE_KEY': 'DOCS',
        'CONFLUENCE_TIMEOUT': '45'
    })
    def test_confluence_loader_with_mock_gcs_storage(self, mocker, mock_config_with_secret,
                                                   confluence_source, test_document):
        """Test complet du ConfluenceLoader avec stockage MockGCS"""

        # Setup MockGCS pour simuler le stockage
        mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")

        # Configuration du bucket de test
        test_bucket = "confluence-integration-bucket"
        test_prefix = "integration-test/confluence"
        mock_gcs.add_bucket(test_bucket)

        # Mock du SyncOrchestrator pour simuler une synchronisation réussie
        with patch('kbotloadscheduler.loader.confluence_loader.SyncOrchestrator') as mock_orchestrator_class:
            mock_orchestrator = MagicMock()
            mock_orchestrator_class.return_value = mock_orchestrator

            # Simuler un résultat de synchronisation réaliste
            mock_sync_result = {
                "sync_id": "integration-test-sync-456789",
                "total_content": 15,
                "processed_content": 15,
                "skipped_content": 0,
                "failed_content": 0,
                "total_attachments": 3,
                "processed_attachments": 3,
                "start_time": "2023-05-27T14:30:00Z",
                "end_time": "2023-05-27T14:35:30Z",
                "duration_seconds": 330,
                "spaces_processed": ["DOCS", "TECH"],
                "content_types": ["page", "blogpost"],
                "storage_location": f"gs://{test_bucket}/{test_prefix}"
            }

            # Mock asyncio pour la synchronisation
            mock_loop = MagicMock()
            with patch('asyncio.new_event_loop', return_value=mock_loop), \
                 patch('asyncio.set_event_loop') as mock_set_event_loop:
                mock_loop.run_until_complete.return_value = mock_sync_result

                # Créer le ConfluenceLoader
                loader = ConfluenceLoader(mock_config_with_secret)

                # Test de get_document avec stockage GCS
                output_path = f"gs://{test_bucket}/{test_prefix}"
                metadata = loader.get_document(confluence_source, test_document, output_path)

                # Vérifications des métadonnées
                assert metadata is not None
                assert isinstance(metadata, dict)

                # Vérifier les métadonnées standard (utiliser les constantes Metadata)
                assert metadata["document_id"] == test_document.id
                assert metadata["document_name"] == test_document.name
                assert metadata["location"] == output_path
                assert metadata["domain"] == confluence_source.domain_code  # Metadata.DOMAIN_CODE = 'domain'
                assert metadata["source"] == confluence_source.code  # Metadata.SOURCE_CODE = 'source'
                assert metadata["source_type"] == "confluence"
                assert metadata["modificationDate"] == test_document.modification_time  # Metadata.MODIFICATION_TIME = 'modificationDate'

                # Vérifier les métadonnées spécifiques à Confluence
                assert "confluence_sync_stats" in metadata
                sync_stats = metadata["confluence_sync_stats"]
                assert sync_stats["sync_id"] == "integration-test-sync-456789"
                assert sync_stats["total_content"] == 15
                assert sync_stats["processed_content"] == 15
                assert sync_stats["spaces_processed"] == ["DOCS", "TECH"]

                # Vérifier que l'orchestrateur a été appelé avec les bonnes configurations
                mock_orchestrator_class.assert_called_once()
                call_args = mock_orchestrator_class.call_args[0]

                # Vérifier la configuration Confluence
                confluence_config = call_args[0]
                assert str(confluence_config.url).rstrip('/') == "https://confluence-integration.example.com"
                assert confluence_config.default_space_key == "DOCS"
                assert confluence_config.timeout == 45

                # Vérifier les critères de recherche
                search_criteria = call_args[1]
                assert search_criteria.spaces == ["DOCS", "TECH"]
                assert search_criteria.max_results == 50
                assert search_criteria.include_attachments is True
                assert search_criteria.content_types == ["page", "blogpost"]
                assert search_criteria.last_modified_days == 7
                assert search_criteria.labels == ["public"]

                # Vérifier la configuration de stockage
                storage_config = call_args[2]
                assert storage_config.storage_type == "gcs"
                assert storage_config.gcs_bucket_name == test_bucket
                assert storage_config.gcs_base_prefix == test_prefix

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence-integration.example.com',
        'DEFAULT_SPACE_KEY': 'DOCS'
    })
    def test_confluence_loader_gcs_path_parsing(self, mock_config_with_secret):
        """Test du parsing des chemins GCS"""
        loader = ConfluenceLoader(mock_config_with_secret)

        # Test avec chemin gs:// complet
        gcs_path = "gs://my-confluence-bucket/data/confluence/docs"
        bucket = loader._extract_bucket_from_path(gcs_path)
        prefix = loader._extract_prefix_from_path(gcs_path)

        assert bucket == "my-confluence-bucket"
        assert prefix == "data/confluence/docs"

        # Test avec chemin sans gs://
        simple_path = "my-confluence-bucket/data/confluence/docs"
        bucket = loader._extract_bucket_from_path(simple_path)
        prefix = loader._extract_prefix_from_path(simple_path)

        assert bucket == "my-confluence-bucket"
        assert prefix == "data/confluence/docs"

        # Test avec bucket seulement
        bucket_only = "gs://my-confluence-bucket"
        bucket = loader._extract_bucket_from_path(bucket_only)
        prefix = loader._extract_prefix_from_path(bucket_only)

        assert bucket == "my-confluence-bucket"
        assert prefix == ""

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence-integration.example.com',
        'DEFAULT_SPACE_KEY': 'DOCS'
    })
    def test_confluence_loader_with_container_integration(self, mocker, mock_config_with_secret):
        """Test d'intégration avec le container de dépendances (si disponible)"""

        try:
            # Essayer d'importer le container
            from kbotloadscheduler.dependency.container import Container

            # Setup MockGCS
            mock_gcs = MockGcs(mocker, "kbotloadscheduler.secret.secret_manager.storage")

            # Mock du secret manager pour éviter les appels GCP
            with patch('kbotloadscheduler.secret.secret_manager.ConfigWithSecret') as mock_secret_class:
                mock_secret_instance = MagicMock()
                mock_secret_instance.get_confluence_credentials.return_value = {
                    "pat_token": "container_test_token"
                }
                mock_secret_class.return_value = mock_secret_instance

                # Créer le container
                container = Container()

                # Récupérer le loader manager
                loader_manager = container.loader_manager()

                # Récupérer le ConfluenceLoader
                confluence_loader = loader_manager.get_loader("confluence")

                # Vérifier que c'est bien notre ConfluenceLoader
                assert isinstance(confluence_loader, ConfluenceLoader)
                assert confluence_loader._loader_type == "confluence"

                print("✅ ConfluenceLoader récupéré avec succès via le container")

        except ImportError:
            # Le container n'est pas disponible (dépendances manquantes)
            pytest.skip("Container non disponible - dépendances manquantes")
        except Exception as e:
            # Autres erreurs (probablement liées à GCP)
            pytest.skip(f"Container non disponible - {str(e)}")

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence-integration.example.com',
        'DEFAULT_SPACE_KEY': 'DOCS'
    })
    def test_confluence_loader_error_handling_with_mock_gcs(self, mocker, mock_config_with_secret,
                                                          confluence_source):
        """Test de la gestion d'erreurs avec MockGCS"""

        # Setup MockGCS
        mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        test_bucket = "error-test-bucket"
        mock_gcs.add_bucket(test_bucket)

        # Mock du SyncOrchestrator pour simuler une erreur
        with patch('kbotloadscheduler.loader.confluence_loader.SyncOrchestrator') as mock_orchestrator_class:
            mock_orchestrator = MagicMock()
            mock_orchestrator_class.return_value = mock_orchestrator

            # Mock asyncio pour simuler une erreur
            mock_loop = MagicMock()
            with patch('asyncio.new_event_loop', return_value=mock_loop), \
                 patch('asyncio.set_event_loop') as mock_set_event_loop:
                mock_loop.run_until_complete.side_effect = Exception("Simulated sync error")

                # Créer le loader
                loader = ConfluenceLoader(mock_config_with_secret)

                # Créer un document de test
                test_document = DocumentBean(
                    id="error_test/doc_123",
                    name="Error Test Document",
                    path="test/path",
                    modification_time=datetime.now(timezone.utc)
                )

                # Tester que l'erreur est correctement gérée
                from kbotloadscheduler.loader.abstract_loader import LoaderException
                with pytest.raises(LoaderException, match="Failed to get document"):
                    loader.get_document(confluence_source, test_document, f"gs://{test_bucket}/error-test")

                # Vérifier que la méthode close du loop est appelée même en cas d'erreur
                mock_loop.close.assert_called_once()
